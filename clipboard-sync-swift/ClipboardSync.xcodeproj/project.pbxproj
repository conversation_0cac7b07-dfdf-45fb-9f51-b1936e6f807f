// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A000001000000000000001 /* ClipboardSyncApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000002000000000000001 /* ClipboardSyncApp.swift */; };
		1A000003000000000000001 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000004000000000000001 /* ContentView.swift */; };
		1A000005000000000000001 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A000006000000000000001 /* Assets.xcassets */; };
		1A000007000000000000001 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A000008000000000000001 /* Preview Assets.xcassets */; };
		1A000009000000000000001 /* ClipboardManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000A000000000000001 /* ClipboardManager.swift */; };
		1A00000B000000000000001 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000C000000000000001 /* NetworkManager.swift */; };
		1A00000D000000000000001 /* ConfigManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000E000000000000001 /* ConfigManager.swift */; };
		1A00000F000000000000001 /* StatusBarManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000010000000000000001 /* StatusBarManager.swift */; };
		1A000011000000000000001 /* ConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000012000000000000001 /* ConfigView.swift */; };
		1A000013000000000000001 /* StatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000014000000000000001 /* StatusView.swift */; };
		1A000015000000000000001 /* LogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000016000000000000001 /* LogView.swift */; };
		1A000017000000000000001 /* Yams in Frameworks */ = {isa = PBXBuildFile; productRef = 1A000018000000000000001 /* Yams */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A000019000000000000001 /* ClipboardSync.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ClipboardSync.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A000002000000000000001 /* ClipboardSyncApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardSyncApp.swift; sourceTree = "<group>"; };
		1A000004000000000000001 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A000006000000000000001 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A000008000000000000001 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A00000A000000000000001 /* ClipboardManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardManager.swift; sourceTree = "<group>"; };
		1A00000C000000000000001 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		1A00000E000000000000001 /* ConfigManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigManager.swift; sourceTree = "<group>"; };
		1A000010000000000000001 /* StatusBarManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatusBarManager.swift; sourceTree = "<group>"; };
		1A000012000000000000001 /* ConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigView.swift; sourceTree = "<group>"; };
		1A000014000000000000001 /* StatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatusView.swift; sourceTree = "<group>"; };
		1A000016000000000000001 /* LogView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogView.swift; sourceTree = "<group>"; };
		1A00001A000000000000001 /* ClipboardSync.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ClipboardSync.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A00001B000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A000017000000000000001 /* Yams in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A00001C000000000000001 = {
			isa = PBXGroup;
			children = (
				1A00001D000000000000001 /* ClipboardSync */,
				1A00001E000000000000001 /* Products */,
			);
			sourceTree = "<group>";
		};
		1A00001D000000000000001 /* ClipboardSync */ = {
			isa = PBXGroup;
			children = (
				1A000002000000000000001 /* ClipboardSyncApp.swift */,
				1A00001F000000000000001 /* Views */,
				1A000020000000000000001 /* Managers */,
				1A000006000000000000001 /* Assets.xcassets */,
				1A00001A000000000000001 /* ClipboardSync.entitlements */,
				1A000021000000000000001 /* Preview Content */,
			);
			path = ClipboardSync;
			sourceTree = "<group>";
		};
		1A00001E000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				1A000019000000000000001 /* ClipboardSync.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A00001F000000000000001 /* Views */ = {
			isa = PBXGroup;
			children = (
				1A000004000000000000001 /* ContentView.swift */,
				1A000012000000000000001 /* ConfigView.swift */,
				1A000014000000000000001 /* StatusView.swift */,
				1A000016000000000000001 /* LogView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A000020000000000000001 /* Managers */ = {
			isa = PBXGroup;
			children = (
				1A00000A000000000000001 /* ClipboardManager.swift */,
				1A00000C000000000000001 /* NetworkManager.swift */,
				1A00000E000000000000001 /* ConfigManager.swift */,
				1A000010000000000000001 /* StatusBarManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		1A000021000000000000001 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A000008000000000000001 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A000022000000000000001 /* ClipboardSync */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A000023000000000000001 /* Build configuration list for PBXNativeTarget "ClipboardSync" */;
			buildPhases = (
				1A000024000000000000001 /* Sources */,
				1A00001B000000000000001 /* Frameworks */,
				1A000025000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ClipboardSync;
			packageProductDependencies = (
				1A000018000000000000001 /* Yams */,
			);
			productName = ClipboardSync;
			productReference = 1A000019000000000000001 /* ClipboardSync.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A000026000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A000022000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A000027000000000000001 /* Build configuration list for PBXProject "ClipboardSync" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A00001C000000000000001;
			packageReferences = (
				1A000028000000000000001 /* XCRemoteSwiftPackageReference "Yams" */,
			);
			productRefGroup = 1A00001E000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A000022000000000000001 /* ClipboardSync */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A000025000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A000007000000000000001 /* Preview Assets.xcassets in Resources */,
				1A000005000000000000001 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A000024000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A000003000000000000001 /* ContentView.swift in Sources */,
				1A000009000000000000001 /* ClipboardManager.swift in Sources */,
				1A00000B000000000000001 /* NetworkManager.swift in Sources */,
				1A00000D000000000000001 /* ConfigManager.swift in Sources */,
				1A00000F000000000000001 /* StatusBarManager.swift in Sources */,
				1A000011000000000000001 /* ConfigView.swift in Sources */,
				1A000013000000000000001 /* StatusView.swift in Sources */,
				1A000015000000000000001 /* LogView.swift in Sources */,
				1A000001000000000000001 /* ClipboardSyncApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A000029000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A00002A000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1A00002B000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ClipboardSync/ClipboardSync.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ClipboardSync/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Clipboard Sync";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clipboardsync.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1A00002C000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ClipboardSync/ClipboardSync.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ClipboardSync/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Clipboard Sync";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clipboardsync.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A000023000000000000001 /* Build configuration list for PBXNativeTarget "ClipboardSync" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A00002B000000000000001 /* Debug */,
				1A00002C000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A000027000000000000001 /* Build configuration list for PBXProject "ClipboardSync" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A000029000000000000001 /* Debug */,
				1A00002A000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		1A000028000000000000001 /* XCRemoteSwiftPackageReference "Yams" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/jpsim/Yams.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1A000018000000000000001 /* Yams */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1A000028000000000000001 /* XCRemoteSwiftPackageReference "Yams" */;
			productName = Yams;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 1A000026000000000000001 /* Project object */;
}
