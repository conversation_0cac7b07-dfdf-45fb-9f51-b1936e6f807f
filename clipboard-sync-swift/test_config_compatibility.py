#!/usr/bin/env python3
"""
测试Swift版本与Python版本的配置兼容性
"""

import yaml
import json
import sys
import os

def load_python_config():
    """加载Python版本的配置"""
    config_path = "../config/config.yaml"
    if not os.path.exists(config_path):
        print(f"❌ Python配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✅ 成功加载Python配置: {config_path}")
        return config
    except Exception as e:
        print(f"❌ 加载Python配置失败: {e}")
        return None

def convert_to_swift_format(python_config):
    """将Python配置转换为Swift格式"""
    swift_config = {
        "sender": {
            "enabled": python_config.get("sender", {}).get("enabled", True),
            "ntfyTopicUrl": python_config.get("sender", {}).get("ntfy_topic_url", ""),
            "pollIntervalSeconds": python_config.get("sender", {}).get("poll_interval_seconds", 1.0),
            "requestTimeoutSeconds": python_config.get("sender", {}).get("request_timeout_seconds", 15),
            "filenamePrefix": python_config.get("sender", {}).get("filename_prefix", "clipboard_content_")
        },
        "receiver": {
            "enabled": python_config.get("receiver", {}).get("enabled", True),
            "ntfyServer": python_config.get("receiver", {}).get("ntfy_server", "ntfy.sh"),
            "ntfyTopic": python_config.get("receiver", {}).get("ntfy_topic", ""),
            "reconnectDelaySeconds": python_config.get("receiver", {}).get("reconnect_delay_seconds", 5),
            "requestTimeoutSeconds": python_config.get("receiver", {}).get("request_timeout_seconds", 15)
        },
        "logging": {
            "level": python_config.get("logging", {}).get("level", "INFO")
        },
        "macos": {
            "imageSupport": python_config.get("macos", {}).get("image_support", True)
        }
    }
    return swift_config

def validate_config_compatibility(python_config, swift_config):
    """验证配置兼容性"""
    print("\n🔍 验证配置兼容性...")
    
    issues = []
    
    # 检查发送器配置
    if python_config.get("sender", {}).get("enabled") != swift_config["sender"]["enabled"]:
        issues.append("发送器启用状态不匹配")
    
    if python_config.get("sender", {}).get("ntfy_topic_url") != swift_config["sender"]["ntfyTopicUrl"]:
        issues.append("发送器ntfy主题URL不匹配")
    
    # 检查接收器配置
    if python_config.get("receiver", {}).get("enabled") != swift_config["receiver"]["enabled"]:
        issues.append("接收器启用状态不匹配")
    
    if python_config.get("receiver", {}).get("ntfy_topic") != swift_config["receiver"]["ntfyTopic"]:
        issues.append("接收器ntfy主题不匹配")
    
    if issues:
        print("❌ 发现配置兼容性问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 配置完全兼容!")
        return True

def test_ntfy_compatibility():
    """测试ntfy协议兼容性"""
    print("\n🔍 测试ntfy协议兼容性...")
    
    # 检查发送URL格式
    python_config = load_python_config()
    if not python_config:
        return False
    
    sender_url = python_config.get("sender", {}).get("ntfy_topic_url", "")
    if not sender_url.startswith("https://ntfy.sh/") and not sender_url.startswith("http://"):
        print("❌ 发送器URL格式可能不兼容")
        return False
    
    # 检查WebSocket URL构建
    receiver_server = python_config.get("receiver", {}).get("ntfy_server", "")
    receiver_topic = python_config.get("receiver", {}).get("ntfy_topic", "")
    
    if not receiver_server or not receiver_topic:
        print("❌ 接收器配置不完整")
        return False
    
    # 构建WebSocket URL（模拟Swift逻辑）
    protocol = "wss" if not receiver_server.startswith("http://") else "ws"
    clean_server = receiver_server.replace("https://", "").replace("http://", "")
    ws_url = f"{protocol}://{clean_server}/{receiver_topic}/ws"
    
    print(f"✅ WebSocket URL: {ws_url}")
    print("✅ ntfy协议兼容!")
    return True

def main():
    print("🧪 Clipboard Sync Swift版本兼容性测试")
    print("=" * 50)
    
    # 加载Python配置
    python_config = load_python_config()
    if not python_config:
        sys.exit(1)
    
    # 转换为Swift格式
    swift_config = convert_to_swift_format(python_config)
    
    # 保存Swift配置示例
    swift_config_path = "swift_config_example.json"
    with open(swift_config_path, 'w', encoding='utf-8') as f:
        json.dump(swift_config, f, indent=2, ensure_ascii=False)
    print(f"✅ Swift配置示例已保存: {swift_config_path}")
    
    # 验证兼容性
    config_compatible = validate_config_compatibility(python_config, swift_config)
    ntfy_compatible = test_ntfy_compatibility()
    
    print("\n📊 测试结果:")
    print(f"   配置兼容性: {'✅ 通过' if config_compatible else '❌ 失败'}")
    print(f"   ntfy协议兼容性: {'✅ 通过' if ntfy_compatible else '❌ 失败'}")
    
    if config_compatible and ntfy_compatible:
        print("\n🎉 Swift版本与Python版本完全兼容!")
        print("   可以安全地在不同设备上运行两个版本进行剪贴板同步。")
        return True
    else:
        print("\n⚠️  发现兼容性问题，需要进一步调整。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
