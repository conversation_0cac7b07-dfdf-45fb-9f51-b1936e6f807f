# Clipboard Sync - macOS Native Swift Version

这是 Clipboard Sync 项目的原生 macOS Swift 实现，提供与 Electron + Python 版本完全相同的功能。

## 功能特性

- ✅ **剪贴板监控**：实时监控本地剪贴板变化（文本和图片）
- ✅ **发送功能**：将剪贴板内容发送到 ntfy 服务器
- ✅ **接收功能**：通过 WebSocket 监听 ntfy 消息并更新本地剪贴板
- ✅ **配置管理**：支持配置发送/接收主题、服务器地址、轮询间隔等
- ✅ **GUI界面**：提供配置界面、状态监控、日志查看
- ✅ **系统托盘**：菜单栏图标，支持快速启停和状态显示
- ✅ **macOS集成**：支持图片剪贴板、系统通知、原生窗口样式
- ✅ **配置兼容**：可导入现有的 `config/config.yaml` 配置文件

## 技术实现

- **SwiftUI**：现代化的用户界面框架
- **URLSession**：网络请求和 WebSocket 通信
- **NSPasteboard**：剪贴板操作
- **UserDefaults**：配置存储
- **NSStatusItem**：系统托盘功能
- **Yams**：YAML 配置文件解析

## 系统要求

- macOS 13.0 或更高版本
- Xcode 15.0 或更高版本

## 构建和运行

### 1. 使用 Xcode

1. 打开 `ClipboardSync.xcodeproj`
2. 选择目标设备（Mac）
3. 点击运行按钮或按 `Cmd+R`

### 2. 使用命令行

```bash
cd clipboard-sync-swift
xcodebuild -project ClipboardSync.xcodeproj -scheme ClipboardSync -configuration Release build
```

## 配置

### 自动导入现有配置

应用启动时会自动尝试从以下位置导入 YAML 配置：

1. 项目根目录的 `config/config.yaml`
2. 应用程序包内的 `config.yaml`
3. 用户主目录的 `~/.clipboard-sync/config.yaml`

### 手动配置

1. 启动应用后，点击菜单栏图标
2. 选择 "Preferences..."
3. 在配置界面中设置：
   - **发送器设置**：ntfy 主题 URL、轮询间隔等
   - **接收器设置**：ntfy 服务器、主题、重连延迟等
   - **高级设置**：日志级别、图片支持等

### 配置示例

```yaml
sender:
  enabled: true
  ntfy_topic_url: "https://ntfy.sh/your_send_topic_here"
  poll_interval_seconds: 1.0
  request_timeout_seconds: 15
  filename_prefix: "clipboard_content_"

receiver:
  enabled: true
  ntfy_server: "ntfy.sh"
  ntfy_topic: "your_receive_topic_here"
  reconnect_delay_seconds: 5
  request_timeout_seconds: 15

logging:
  level: "INFO"

macos:
  image_support: true
```

## 使用方法

### 启动同步

1. **通过菜单栏**：点击菜单栏图标 → "Start Sync"
2. **通过主界面**：打开 Preferences → Status 标签 → "Start Sync"

### 停止同步

1. **通过菜单栏**：点击菜单栏图标 → "Stop Sync"
2. **通过主界面**：打开 Preferences → Status 标签 → "Stop Sync"

### 查看状态和日志

- **状态监控**：Status 标签显示连接状态、统计信息
- **日志查看**：Logs 标签显示详细的运行日志
- **菜单栏状态**：图标显示当前运行状态

## 与 Python 版本的互操作性

Swift 版本与现有的 Python/Electron 版本完全兼容：

- 使用相同的 ntfy 协议和消息格式
- 支持相同的配置文件格式
- 可以与 Python 版本同时运行在不同设备上
- 支持文本和图片的双向同步

## 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查网络连接
   - 确认 ntfy 服务器地址正确
   - 查看 Logs 标签中的错误信息

2. **剪贴板不同步**
   - 确认发送器和接收器都已启用
   - 检查 ntfy 主题配置是否正确
   - 查看统计信息确认消息发送/接收状态

3. **图片同步问题**
   - 确认 "Enable macOS Image Support" 已开启
   - 检查图片格式是否支持（PNG、JPEG 等）

### 日志级别

- **DEBUG**：详细的调试信息
- **INFO**：一般信息（推荐）
- **WARNING**：警告信息
- **ERROR**：仅错误信息

## 开发

### 项目结构

```
ClipboardSync/
├── ClipboardSyncApp.swift          # 主应用入口
├── Views/                          # SwiftUI 视图
│   ├── ContentView.swift          # 主视图
│   ├── ConfigView.swift           # 配置界面
│   ├── StatusView.swift           # 状态监控
│   └── LogView.swift              # 日志查看
└── Managers/                       # 核心管理器
    ├── ClipboardManager.swift     # 剪贴板管理
    ├── NetworkManager.swift       # 网络通信
    ├── ConfigManager.swift        # 配置管理
    └── StatusBarManager.swift     # 状态栏管理
```

### 依赖

- **Yams**：YAML 解析库（通过 Swift Package Manager）

## 许可证

与主项目保持一致的许可证。
