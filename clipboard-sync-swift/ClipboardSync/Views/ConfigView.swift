import SwiftUI

struct ConfigView: View {
    @EnvironmentObject var configManager: ConfigManager
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 发送器配置
                GroupBox("Sender Settings") {
                    VStack(alignment: .leading, spacing: 12) {
                        Toggle("Enable Sender", isOn: $configManager.config.sender.enabled)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Ntfy Topic URL:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            TextField("https://ntfy.sh/your_topic_here", text: $configManager.config.sender.ntfyTopicUrl)
                                .textFieldStyle(.roundedBorder)
                        }
                        
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Poll Interval (seconds):")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("1.0", value: $configManager.config.sender.pollIntervalSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Request Timeout (seconds):")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("15", value: $configManager.config.sender.requestTimeoutSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Filename Prefix:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            TextField("clipboard_content_", text: $configManager.config.sender.filenamePrefix)
                                .textFieldStyle(.roundedBorder)
                        }
                    }
                    .padding()
                }
                
                // 接收器配置
                GroupBox("Receiver Settings") {
                    VStack(alignment: .leading, spacing: 12) {
                        Toggle("Enable Receiver", isOn: $configManager.config.receiver.enabled)
                        
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Ntfy Server:")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("ntfy.sh", text: $configManager.config.receiver.ntfyServer)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Ntfy Topic:")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("your_receive_topic_here", text: $configManager.config.receiver.ntfyTopic)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                        
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Reconnect Delay (seconds):")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("5", value: $configManager.config.receiver.reconnectDelaySeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Request Timeout (seconds):")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                TextField("15", value: $configManager.config.receiver.requestTimeoutSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                    }
                    .padding()
                }
                
                // 高级设置
                GroupBox("Advanced Settings") {
                    VStack(alignment: .leading, spacing: 12) {
                        Picker("Log Level:", selection: $configManager.config.logging.level) {
                            Text("Debug").tag("DEBUG")
                            Text("Info").tag("INFO")
                            Text("Warning").tag("WARNING")
                            Text("Error").tag("ERROR")
                        }
                        .pickerStyle(.menu)
                        
                        Toggle("Enable macOS Image Support", isOn: $configManager.config.macos.imageSupport)
                    }
                    .padding()
                }
                
                // 操作按钮
                HStack {
                    Button("Load from YAML") {
                        loadFromYAML()
                    }
                    
                    Spacer()
                    
                    Button("Save Configuration") {
                        saveConfiguration()
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .padding()
        }
        .alert("Configuration", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func loadFromYAML() {
        if configManager.loadFromYAMLFile() {
            alertMessage = "Configuration loaded from YAML file successfully!"
        } else {
            alertMessage = "Failed to load configuration from YAML file."
        }
        showingAlert = true
    }
    
    private func saveConfiguration() {
        if configManager.saveConfiguration() {
            alertMessage = "Configuration saved successfully!"
        } else {
            alertMessage = "Failed to save configuration."
        }
        showingAlert = true
    }
}
