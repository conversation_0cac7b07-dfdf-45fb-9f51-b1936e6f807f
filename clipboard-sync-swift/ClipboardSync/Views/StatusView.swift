import SwiftUI

struct StatusView: View {
    @EnvironmentObject var configManager: ConfigManager
    @EnvironmentObject var clipboardManager: ClipboardManager
    @EnvironmentObject var networkManager: NetworkManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 同步状态
            GroupBox("Sync Status") {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Status:")
                            .fontWeight(.medium)
                        Spacer()
                        HStack(spacing: 8) {
                            Circle()
                                .fill(clipboardManager.isRunning ? Color.green : Color.red)
                                .frame(width: 12, height: 12)
                            Text(clipboardManager.isRunning ? "Running" : "Stopped")
                                .foregroundColor(clipboardManager.isRunning ? .green : .red)
                                .fontWeight(.medium)
                        }
                    }
                    
                    Divider()
                    
                    HStack {
                        Text("Sender:")
                            .fontWeight(.medium)
                        Spacer()
                        Text(configManager.config.sender.enabled ? "Enabled" : "Disabled")
                            .foregroundColor(configManager.config.sender.enabled ? .green : .secondary)
                    }
                    
                    HStack {
                        Text("Receiver:")
                            .fontWeight(.medium)
                        Spacer()
                        Text(configManager.config.receiver.enabled ? "Enabled" : "Disabled")
                            .foregroundColor(configManager.config.receiver.enabled ? .green : .secondary)
                    }
                    
                    if clipboardManager.isRunning {
                        Divider()
                        
                        HStack {
                            Text("WebSocket:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(networkManager.isWebSocketConnected ? "Connected" : "Disconnected")
                                .foregroundColor(networkManager.isWebSocketConnected ? .green : .orange)
                        }
                        
                        HStack {
                            Text("Last Activity:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(clipboardManager.lastActivity.isEmpty ? "None" : clipboardManager.lastActivity)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
            }
            
            // 统计信息
            GroupBox("Statistics") {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Messages Sent:")
                            .fontWeight(.medium)
                        Spacer()
                        Text("\(clipboardManager.messagesSent)")
                            .foregroundColor(.blue)
                    }
                    
                    HStack {
                        Text("Messages Received:")
                            .fontWeight(.medium)
                        Spacer()
                        Text("\(clipboardManager.messagesReceived)")
                            .foregroundColor(.blue)
                    }
                    
                    HStack {
                        Text("Connection Errors:")
                            .fontWeight(.medium)
                        Spacer()
                        Text("\(networkManager.connectionErrors)")
                            .foregroundColor(.red)
                    }
                }
                .padding()
            }
            
            // 控制按钮
            HStack {
                Button(clipboardManager.isRunning ? "Stop Sync" : "Start Sync") {
                    if clipboardManager.isRunning {
                        clipboardManager.stopMonitoring()
                        networkManager.disconnect()
                    } else {
                        clipboardManager.startMonitoring()
                        if configManager.config.receiver.enabled {
                            networkManager.connect()
                        }
                    }
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                
                Spacer()
                
                Button("Reset Statistics") {
                    clipboardManager.resetStatistics()
                    networkManager.resetStatistics()
                }
                .buttonStyle(.bordered)
            }
            
            Spacer()
        }
        .padding()
    }
}
