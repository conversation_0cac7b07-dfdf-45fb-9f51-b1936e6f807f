import SwiftUI

struct ContentView: View {
    @EnvironmentObject var configManager: ConfigManager
    @EnvironmentObject var clipboardManager: ClipboardManager
    @EnvironmentObject var networkManager: NetworkManager
    @EnvironmentObject var statusBarManager: StatusBarManager
    
    @State private var selectedTab = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("Clipboard Sync")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // 状态指示器
                HStack(spacing: 8) {
                    Circle()
                        .fill(clipboardManager.isRunning ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    
                    Text(clipboardManager.isRunning ? "Running" : "Stopped")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            Divider()
            
            // 标签页选择器
            Picker("Tab", selection: $selectedTab) {
                Text("Configuration").tag(0)
                Text("Status").tag(1)
                Text("Logs").tag(2)
            }
            .pickerStyle(.segmented)
            .padding(.horizontal)
            .padding(.top, 8)
            
            // 内容区域
            TabView(selection: $selectedTab) {
                ConfigView()
                    .tag(0)
                
                StatusView()
                    .tag(1)
                
                LogView()
                    .tag(2)
            }
            .tabViewStyle(.automatic)
        }
        .frame(width: 600, height: 500)
        .background(Color(NSColor.windowBackgroundColor))
    }
}
