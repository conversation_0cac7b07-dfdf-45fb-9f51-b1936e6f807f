import SwiftUI

struct LogView: View {
    @EnvironmentObject var clipboardManager: ClipboardManager
    @State private var autoScroll = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 控制栏
            HStack {
                Text("Logs")
                    .font(.headline)
                
                Spacer()
                
                Toggle("Auto Scroll", isOn: $autoScroll)
                    .toggleStyle(.switch)
                    .controlSize(.mini)
                
                Button("Clear Logs") {
                    clipboardManager.clearLogs()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
            .padding(.horizontal)
            .padding(.top)
            
            Divider()
            
            // 日志内容
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        if clipboardManager.logs.isEmpty {
                            Text("No logs available. Start sync to see output.")
                                .foregroundColor(.secondary)
                                .italic()
                                .padding()
                        } else {
                            ForEach(Array(clipboardManager.logs.enumerated()), id: \.offset) { index, log in
                                LogEntryView(log: log)
                                    .id(index)
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .onChange(of: clipboardManager.logs.count) { _ in
                    if autoScroll && !clipboardManager.logs.isEmpty {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(clipboardManager.logs.count - 1, anchor: .bottom)
                        }
                    }
                }
            }
        }
        .background(Color(NSColor.textBackgroundColor))
    }
}

struct LogEntryView: View {
    let log: LogEntry
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // 时间戳
            Text(log.timestamp, style: .time)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .leading)
            
            // 日志级别
            Text(log.level.rawValue)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(log.level.color)
                .frame(width: 50, alignment: .leading)
            
            // 消息
            Text(log.message)
                .font(.caption)
                .foregroundColor(.primary)
                .textSelection(.enabled)
            
            Spacer()
        }
        .padding(.vertical, 2)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(log.level == .error ? Color.red.opacity(0.1) : Color.clear)
        )
    }
}

struct LogEntry {
    let timestamp: Date
    let level: LogLevel
    let message: String
    
    init(level: LogLevel, message: String) {
        self.timestamp = Date()
        self.level = level
        self.message = message
    }
}

enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARN"
    case error = "ERROR"
    
    var color: Color {
        switch self {
        case .debug:
            return .secondary
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        }
    }
}
