import SwiftUI
import AppKit

@main
struct ClipboardSyncApp: App {
    @StateObject private var configManager = ConfigManager()
    @StateObject private var clipboardManager = ClipboardManager()
    @StateObject private var networkManager = NetworkManager()
    @StateObject private var statusBarManager = StatusBarManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(configManager)
                .environmentObject(clipboardManager)
                .environmentObject(networkManager)
                .environmentObject(statusBarManager)
                .onAppear {
                    setupApp()
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .commands {
            CommandGroup(replacing: .newItem) { }
        }
    }
    
    private func setupApp() {
        // 设置状态栏
        statusBarManager.setup(
            configManager: configManager,
            clipboardManager: clipboardManager,
            networkManager: networkManager
        )
        
        // 加载配置
        configManager.loadConfiguration()
        
        // 设置网络管理器
        networkManager.configure(with: configManager.config)
        networkManager.setClipboardManager(clipboardManager)

        // 设置剪贴板管理器
        clipboardManager.configure(
            configManager: configManager,
            networkManager: networkManager
        )
        
        // 隐藏主窗口（只显示状态栏）
        if let window = NSApplication.shared.windows.first {
            window.setIsVisible(false)
        }
    }
}
