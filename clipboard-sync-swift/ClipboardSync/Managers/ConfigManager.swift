import Foundation
import Yams

class ConfigManager: ObservableObject {
    @Published var config = AppConfig()
    
    private let userDefaults = UserDefaults.standard
    private let configKey = "ClipboardSyncConfig"
    
    init() {
        loadConfiguration()
    }
    
    func loadConfiguration() {
        // 首先尝试从UserDefaults加载
        if let data = userDefaults.data(forKey: configKey),
           let decodedConfig = try? JSONDecoder().decode(AppConfig.self, from: data) {
            config = decodedConfig
        } else {
            // 如果UserDefaults中没有，尝试从YAML文件加载
            _ = loadFromYAMLFile()
        }
    }
    
    func saveConfiguration() -> Bool {
        do {
            let data = try JSONEncoder().encode(config)
            userDefaults.set(data, forKey: configKey)
            return true
        } catch {
            print("Failed to save configuration: \(error)")
            return false
        }
    }
    
    func loadFromYAMLFile() -> Bool {
        // 尝试从多个可能的位置加载YAML配置文件
        let possiblePaths = [
            // 相对于应用程序的路径
            Bundle.main.path(forResource: "config", ofType: "yaml"),
            // 项目根目录的config文件夹
            findProjectConfigPath(),
            // 用户主目录
            NSHomeDirectory() + "/.clipboard-sync/config.yaml"
        ]
        
        for path in possiblePaths {
            if let path = path, loadYAMLFromPath(path) {
                return true
            }
        }
        
        return false
    }
    
    private func findProjectConfigPath() -> String? {
        // 尝试找到项目根目录的config.yaml文件
        let currentPath = FileManager.default.currentDirectoryPath
        let possiblePaths = [
            currentPath + "/config/config.yaml",
            currentPath + "/../config/config.yaml",
            currentPath + "/../../config/config.yaml"
        ]
        
        for path in possiblePaths {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }
        
        return nil
    }
    
    private func loadYAMLFromPath(_ path: String) -> Bool {
        guard FileManager.default.fileExists(atPath: path) else {
            return false
        }
        
        do {
            let yamlString = try String(contentsOfFile: path, encoding: .utf8)
            let yamlData = try Yams.load(yaml: yamlString) as? [String: Any]
            
            if let yamlData = yamlData {
                config = parseYAMLToConfig(yamlData)
                // 保存到UserDefaults
                _ = saveConfiguration()
                return true
            }
        } catch {
            print("Failed to load YAML from \(path): \(error)")
        }
        
        return false
    }
    
    private func parseYAMLToConfig(_ yaml: [String: Any]) -> AppConfig {
        var newConfig = AppConfig()
        
        // 解析sender配置
        if let sender = yaml["sender"] as? [String: Any] {
            newConfig.sender.enabled = sender["enabled"] as? Bool ?? false
            newConfig.sender.ntfyTopicUrl = sender["ntfy_topic_url"] as? String ?? ""
            newConfig.sender.pollIntervalSeconds = sender["poll_interval_seconds"] as? Double ?? 1.0
            newConfig.sender.requestTimeoutSeconds = sender["request_timeout_seconds"] as? Int ?? 15
            newConfig.sender.filenamePrefix = sender["filename_prefix"] as? String ?? "clipboard_content_"
        }
        
        // 解析receiver配置
        if let receiver = yaml["receiver"] as? [String: Any] {
            newConfig.receiver.enabled = receiver["enabled"] as? Bool ?? false
            newConfig.receiver.ntfyServer = receiver["ntfy_server"] as? String ?? "ntfy.sh"
            newConfig.receiver.ntfyTopic = receiver["ntfy_topic"] as? String ?? ""
            newConfig.receiver.reconnectDelaySeconds = receiver["reconnect_delay_seconds"] as? Int ?? 5
            newConfig.receiver.requestTimeoutSeconds = receiver["request_timeout_seconds"] as? Int ?? 15
        }
        
        // 解析logging配置
        if let logging = yaml["logging"] as? [String: Any] {
            newConfig.logging.level = logging["level"] as? String ?? "INFO"
        }
        
        // 解析macOS配置
        if let macos = yaml["macos"] as? [String: Any] {
            newConfig.macos.imageSupport = macos["image_support"] as? Bool ?? true
        }
        
        return newConfig
    }
    
    func exportToYAML() -> String {
        let yamlDict: [String: Any] = [
            "sender": [
                "enabled": config.sender.enabled,
                "ntfy_topic_url": config.sender.ntfyTopicUrl,
                "poll_interval_seconds": config.sender.pollIntervalSeconds,
                "request_timeout_seconds": config.sender.requestTimeoutSeconds,
                "filename_prefix": config.sender.filenamePrefix
            ],
            "receiver": [
                "enabled": config.receiver.enabled,
                "ntfy_server": config.receiver.ntfyServer,
                "ntfy_topic": config.receiver.ntfyTopic,
                "reconnect_delay_seconds": config.receiver.reconnectDelaySeconds,
                "request_timeout_seconds": config.receiver.requestTimeoutSeconds
            ],
            "logging": [
                "level": config.logging.level
            ],
            "macos": [
                "image_support": config.macos.imageSupport
            ]
        ]
        
        do {
            return try Yams.dump(object: yamlDict)
        } catch {
            return "# Failed to export configuration: \(error)"
        }
    }
}

// MARK: - Configuration Models

struct AppConfig: Codable {
    var sender = SenderConfig()
    var receiver = ReceiverConfig()
    var logging = LoggingConfig()
    var macos = MacOSConfig()
}

struct SenderConfig: Codable {
    var enabled = true
    var ntfyTopicUrl = "https://ntfy.sh/YOUR_SEND_TOPIC_HERE"
    var pollIntervalSeconds = 1.0
    var requestTimeoutSeconds = 15
    var filenamePrefix = "clipboard_content_"
}

struct ReceiverConfig: Codable {
    var enabled = true
    var ntfyServer = "ntfy.sh"
    var ntfyTopic = "YOUR_RECEIVE_TOPIC_HERE"
    var reconnectDelaySeconds = 5
    var requestTimeoutSeconds = 15
}

struct LoggingConfig: Codable {
    var level = "INFO"
}

struct MacOSConfig: Codable {
    var imageSupport = true
}
