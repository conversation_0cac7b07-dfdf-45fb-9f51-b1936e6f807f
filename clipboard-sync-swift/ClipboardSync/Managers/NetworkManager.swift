import Foundation
import AppKit
import Network

class NetworkManager: NSObject, ObservableObject {
    @Published var isWebSocketConnected = false
    @Published var connectionErrors = 0
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var clipboardManager: ClipboardManager?
    private var config: AppConfig?
    
    override init() {
        super.init()
        setupURLSession()
    }
    
    private func setupURLSession() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }
    
    func configure(with config: AppConfig) {
        self.config = config
    }

    func setClipboardManager(_ clipboardManager: ClipboardManager) {
        self.clipboardManager = clipboardManager
    }
    
    // MARK: - HTTP POST Methods
    
    func sendText(_ text: String, config: AppConfig) async throws {
        let filename = "\(config.sender.filenamePrefix)\(generateRandomString()).txt"
        let textData = text.data(using: .utf8) ?? Data()
        
        try await sendToNtfy(data: textData, filename: filename, contentType: "text/plain", config: config)
    }
    
    func sendImage(_ image: NSImage, config: AppConfig) async throws {
        guard let imageData = image.tiffRepresentation,
              let bitmap = NSBitmapImageRep(data: imageData),
              let pngData = bitmap.representation(using: .png, properties: [:]) else {
            throw NetworkError.imageConversionFailed
        }
        
        let filename = "\(config.sender.filenamePrefix)\(generateRandomString()).png"
        
        try await sendToNtfy(data: pngData, filename: filename, contentType: "image/png", config: config)
    }
    
    private func sendToNtfy(data: Data, filename: String, contentType: String, config: AppConfig) async throws {
        guard let url = URL(string: config.sender.ntfyTopicUrl) else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(contentType, forHTTPHeaderField: "Content-Type")
        request.setValue(filename, forHTTPHeaderField: "Filename")
        request.setValue("attachment", forHTTPHeaderField: "Content-Disposition")
        request.timeoutInterval = TimeInterval(config.sender.requestTimeoutSeconds)
        request.httpBody = data
        
        clipboardManager?.addLog(.info, "Attempting to POST file: \(filename) (\(data.count) bytes) to \(config.sender.ntfyTopicUrl)")
        
        let (_, response) = try await urlSession!.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NetworkError.invalidResponse
        }
        
        if httpResponse.statusCode == 200 {
            clipboardManager?.addLog(.info, "Successfully POSTed to ntfy. Status: \(httpResponse.statusCode)")
        } else {
            clipboardManager?.addLog(.error, "Failed to POST to ntfy. Status: \(httpResponse.statusCode)")
            throw NetworkError.httpError(httpResponse.statusCode)
        }
    }
    
    // MARK: - WebSocket Methods
    
    func connect() {
        guard let config = config,
              config.receiver.enabled else { return }
        
        let wsURLString = buildWebSocketURL(config: config)
        guard let wsURL = URL(string: wsURLString) else {
            clipboardManager?.addLog(.error, "Invalid WebSocket URL: \(wsURLString)")
            return
        }
        
        clipboardManager?.addLog(.info, "Attempting to connect to WebSocket: \(wsURLString)")
        
        webSocketTask = urlSession?.webSocketTask(with: wsURL)
        webSocketTask?.resume()
        
        // 开始接收消息
        receiveMessage()
    }
    
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        isWebSocketConnected = false
        clipboardManager?.addLog(.info, "WebSocket disconnected")
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                self?.handleWebSocketMessage(message)
                // 继续接收下一条消息
                self?.receiveMessage()
                
            case .failure(let error):
                DispatchQueue.main.async {
                    self?.isWebSocketConnected = false
                    self?.connectionErrors += 1
                    self?.clipboardManager?.addLog(.error, "WebSocket error: \(error.localizedDescription)")
                }
                
                // 尝试重连
                self?.scheduleReconnect()
            }
        }
    }
    
    private func handleWebSocketMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .string(let text):
            if text.contains("open") {
                DispatchQueue.main.async {
                    self.isWebSocketConnected = true
                    self.clipboardManager?.addLog(.info, "Successfully connected to ntfy topic via WebSocket")
                    self.clipboardManager?.addLog(.info, "WebSocket connection confirmed open by ntfy")
                }
            } else {
                // 解析ntfy消息
                parseNtfyMessage(text)
            }
            
        case .data(let data):
            // 处理二进制数据（图片等）
            DispatchQueue.main.async {
                self.clipboardManager?.updateClipboard(with: data)
            }
            
        @unknown default:
            break
        }
    }
    
    private func parseNtfyMessage(_ jsonString: String) {
        guard let data = jsonString.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return
        }
        
        // 检查是否有附件
        if let attachment = json["attachment"] as? [String: Any],
           let url = attachment["url"] as? String {
            downloadAttachment(from: url)
        } else if let message = json["message"] as? String, !message.isEmpty {
            // 处理文本消息
            DispatchQueue.main.async {
                self.clipboardManager?.updateClipboard(with: message)
            }
        }
    }
    
    private func downloadAttachment(from urlString: String) {
        guard let url = URL(string: urlString),
              let config = config else { return }
        
        Task {
            do {
                var request = URLRequest(url: url)
                request.timeoutInterval = TimeInterval(config.receiver.requestTimeoutSeconds)
                
                let (data, _) = try await urlSession!.data(for: request)
                
                // 尝试判断是否为图片
                if NSImage(data: data) != nil {
                    await MainActor.run {
                        clipboardManager?.updateClipboard(with: data)
                    }
                } else {
                    // 作为文本处理
                    if let text = String(data: data, encoding: .utf8) {
                        await MainActor.run {
                            clipboardManager?.updateClipboard(with: text)
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    clipboardManager?.addLog(.error, "Failed to download attachment: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func scheduleReconnect() {
        guard let config = config else { return }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + TimeInterval(config.receiver.reconnectDelaySeconds)) {
            if self.config?.receiver.enabled == true {
                self.connect()
            }
        }
    }
    
    private func buildWebSocketURL(config: AppConfig) -> String {
        let server = config.receiver.ntfyServer
        let topic = config.receiver.ntfyTopic
        
        let wsProtocol = server.hasPrefix("http://") ? "ws" : "wss"
        let cleanServer = server.replacingOccurrences(of: "https://", with: "")
                                .replacingOccurrences(of: "http://", with: "")
        
        return "\(wsProtocol)://\(cleanServer)/\(topic)/ws"
    }
    
    private func generateRandomString() -> String {
        let letters = "abcdefghijklmnopqrstuvwxyz0123456789"
        return String((0..<8).map { _ in letters.randomElement()! })
    }
    
    func resetStatistics() {
        connectionErrors = 0
    }
}

// MARK: - URLSessionDelegate

extension NetworkManager: URLSessionDelegate, URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        DispatchQueue.main.async {
            self.isWebSocketConnected = true
            self.clipboardManager?.addLog(.info, "WebSocket connection opened")
        }
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        DispatchQueue.main.async {
            self.isWebSocketConnected = false
            self.clipboardManager?.addLog(.warning, "WebSocket connection closed")
        }
    }
}

enum NetworkError: Error {
    case invalidURL
    case invalidResponse
    case httpError(Int)
    case imageConversionFailed
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .imageConversionFailed:
            return "Failed to convert image"
        }
    }
}
