import Foundation
import AppKit
import SwiftUI

class ClipboardManager: ObservableObject {
    @Published var isRunning = false
    @Published var lastActivity = ""
    @Published var messagesSent = 0
    @Published var messagesReceived = 0
    @Published var logs: [LogEntry] = []
    
    private var configManager: ConfigManager?
    private var networkManager: NetworkManager?
    private var monitoringTimer: Timer?
    private var lastChangeCount: Int = 0
    private var lastReceivedText: String?
    
    private let pasteboard = NSPasteboard.general
    
    func configure(configManager: ConfigManager, networkManager: NetworkManager) {
        self.configManager = configManager
        self.networkManager = networkManager
        self.lastChangeCount = pasteboard.changeCount
    }
    
    func startMonitoring() {
        guard let configManager = configManager else { return }
        
        isRunning = true
        lastChangeCount = pasteboard.changeCount
        
        addLog(.info, "Starting clipboard monitoring...")
        
        // 启动定时器监控剪贴板变化
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: configManager.config.sender.pollIntervalSeconds, repeats: true) { _ in
            self.checkClipboardChanges()
        }
        
        lastActivity = "Started monitoring"
    }
    
    func stopMonitoring() {
        isRunning = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        addLog(.info, "Stopped clipboard monitoring")
        lastActivity = "Stopped monitoring"
    }
    
    private func checkClipboardChanges() {
        guard let configManager = configManager,
              let networkManager = networkManager,
              configManager.config.sender.enabled else { return }
        
        let currentChangeCount = pasteboard.changeCount
        
        // 检查剪贴板是否有变化
        if currentChangeCount != lastChangeCount {
            lastChangeCount = currentChangeCount
            
            // 处理文本内容
            if let text = pasteboard.string(forType: .string) {
                handleTextChange(text, networkManager: networkManager, config: configManager.config)
            }
            
            // 处理图片内容（如果启用）
            if configManager.config.macos.imageSupport {
                if let image = pasteboard.readObjects(forClasses: [NSImage.self], options: nil)?.first as? NSImage {
                    handleImageChange(image, networkManager: networkManager, config: configManager.config)
                }
            }
        }
    }
    
    private func handleTextChange(_ text: String, networkManager: NetworkManager, config: AppConfig) {
        // 避免发送刚刚接收到的内容
        if text == lastReceivedText {
            lastReceivedText = nil
            return
        }
        
        addLog(.info, "Detected new clipboard text, preparing to send...")
        
        Task {
            do {
                try await networkManager.sendText(text, config: config)
                await MainActor.run {
                    messagesSent += 1
                    lastActivity = "Sent text (\(text.count) chars)"
                    addLog(.info, "Successfully sent clipboard text to ntfy")
                }
            } catch {
                await MainActor.run {
                    addLog(.error, "Failed to send text: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func handleImageChange(_ image: NSImage, networkManager: NetworkManager, config: AppConfig) {
        addLog(.info, "Detected new clipboard image, preparing to send...")
        
        Task {
            do {
                try await networkManager.sendImage(image, config: config)
                await MainActor.run {
                    messagesSent += 1
                    lastActivity = "Sent image"
                    addLog(.info, "Successfully sent clipboard image to ntfy")
                }
            } catch {
                await MainActor.run {
                    addLog(.error, "Failed to send image: \(error.localizedDescription)")
                }
            }
        }
    }
    
    func updateClipboard(with text: String) {
        lastReceivedText = text
        pasteboard.clearContents()
        pasteboard.setString(text, forType: .string)
        
        messagesReceived += 1
        lastActivity = "Received text (\(text.count) chars)"
        addLog(.info, "Updated clipboard with received text")
    }
    
    func updateClipboard(with imageData: Data) {
        if let image = NSImage(data: imageData) {
            pasteboard.clearContents()
            pasteboard.writeObjects([image])
            
            messagesReceived += 1
            lastActivity = "Received image"
            addLog(.info, "Updated clipboard with received image")
        }
    }
    
    func resetStatistics() {
        messagesSent = 0
        messagesReceived = 0
        addLog(.info, "Statistics reset")
    }
    
    func addLog(_ level: LogLevel, _ message: String) {
        DispatchQueue.main.async {
            self.logs.append(LogEntry(level: level, message: message))
            
            // 保持最多1000条日志
            if self.logs.count > 1000 {
                self.logs.removeFirst(self.logs.count - 1000)
            }
        }
    }
    
    func clearLogs() {
        logs.removeAll()
    }
}
