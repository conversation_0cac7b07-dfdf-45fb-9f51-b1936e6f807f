import SwiftUI
import AppKit

class StatusBarManager: ObservableObject {
    private var statusItem: NSStatusItem?
    private var configManager: ConfigManager?
    private var clipboardManager: ClipboardManager?
    private var networkManager: NetworkManager?
    
    func setup(configManager: ConfigManager, clipboardManager: ClipboardManager, networkManager: NetworkManager) {
        self.configManager = configManager
        self.clipboardManager = clipboardManager
        self.networkManager = networkManager
        
        createStatusItem()
        updateMenu()
        
        // 监听状态变化
        setupObservers()
    }
    
    private func createStatusItem() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        
        if let button = statusItem?.button {
            // 使用系统图标或自定义图标
            button.image = NSImage(systemSymbolName: "doc.on.clipboard", accessibilityDescription: "Clipboard Sync")
            button.image?.isTemplate = true
            button.toolTip = "Clipboard Sync"
        }
    }
    
    private func setupObservers() {
        // 监听剪贴板管理器状态变化
        clipboardManager?.$isRunning
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateMenu()
            }
            .store(in: &cancellables)
        
        // 监听网络连接状态变化
        networkManager?.$isWebSocketConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateMenu()
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    private func updateMenu() {
        guard let clipboardManager = clipboardManager,
              let networkManager = networkManager else { return }
        
        let menu = NSMenu()
        
        // 应用标题
        let titleItem = NSMenuItem(title: "Clipboard Sync", action: nil, keyEquivalent: "")
        titleItem.isEnabled = false
        menu.addItem(titleItem)
        
        menu.addItem(NSMenuItem.separator())
        
        // 状态信息
        let statusText = clipboardManager.isRunning ? "Running" : "Stopped"
        let statusMenuItem = NSMenuItem(title: "Status: \(statusText)", action: nil, keyEquivalent: "")
        statusMenuItem.isEnabled = false
        menu.addItem(statusMenuItem)
        
        if clipboardManager.isRunning {
            let wsStatus = networkManager.isWebSocketConnected ? "Connected" : "Disconnected"
            let wsItem = NSMenuItem(title: "WebSocket: \(wsStatus)", action: nil, keyEquivalent: "")
            wsItem.isEnabled = false
            menu.addItem(wsItem)
        }
        
        menu.addItem(NSMenuItem.separator())
        
        // 控制按钮
        let toggleTitle = clipboardManager.isRunning ? "Stop Sync" : "Start Sync"
        let toggleItem = NSMenuItem(title: toggleTitle, action: #selector(toggleSync), keyEquivalent: "")
        toggleItem.target = self
        menu.addItem(toggleItem)
        
        menu.addItem(NSMenuItem.separator())
        
        // 统计信息
        let sentItem = NSMenuItem(title: "Sent: \(clipboardManager.messagesSent)", action: nil, keyEquivalent: "")
        sentItem.isEnabled = false
        menu.addItem(sentItem)
        
        let receivedItem = NSMenuItem(title: "Received: \(clipboardManager.messagesReceived)", action: nil, keyEquivalent: "")
        receivedItem.isEnabled = false
        menu.addItem(receivedItem)
        
        if networkManager.connectionErrors > 0 {
            let errorsItem = NSMenuItem(title: "Errors: \(networkManager.connectionErrors)", action: nil, keyEquivalent: "")
            errorsItem.isEnabled = false
            menu.addItem(errorsItem)
        }
        
        menu.addItem(NSMenuItem.separator())
        
        // 配置菜单
        let configItem = NSMenuItem(title: "Preferences...", action: #selector(showPreferences), keyEquivalent: ",")
        configItem.target = self
        menu.addItem(configItem)
        
        // 重置统计
        let resetItem = NSMenuItem(title: "Reset Statistics", action: #selector(resetStatistics), keyEquivalent: "")
        resetItem.target = self
        menu.addItem(resetItem)
        
        menu.addItem(NSMenuItem.separator())
        
        // 退出
        let quitItem = NSMenuItem(title: "Quit Clipboard Sync", action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)
        
        self.statusItem?.menu = menu
        
        // 更新图标状态
        updateStatusIcon(isRunning: clipboardManager.isRunning)
    }
    
    private func updateStatusIcon(isRunning: Bool) {
        if let button = statusItem?.button {
            let iconName = isRunning ? "doc.on.clipboard.fill" : "doc.on.clipboard"
            button.image = NSImage(systemSymbolName: iconName, accessibilityDescription: "Clipboard Sync")
            button.image?.isTemplate = true
        }
    }
    
    @objc private func toggleSync() {
        guard let clipboardManager = clipboardManager,
              let networkManager = networkManager,
              let configManager = configManager else { return }
        
        if clipboardManager.isRunning {
            clipboardManager.stopMonitoring()
            networkManager.disconnect()
        } else {
            clipboardManager.startMonitoring()
            if configManager.config.receiver.enabled {
                networkManager.connect()
            }
        }
    }
    
    @objc private func showPreferences() {
        // 显示主窗口
        if let window = NSApplication.shared.windows.first {
            window.setIsVisible(true)
            window.makeKeyAndOrderFront(nil)
            NSApplication.shared.activate(ignoringOtherApps: true)
        }
    }
    
    @objc private func resetStatistics() {
        clipboardManager?.resetStatistics()
        networkManager?.resetStatistics()
    }
    
    @objc private func quitApp() {
        // 停止所有活动
        clipboardManager?.stopMonitoring()
        networkManager?.disconnect()
        
        // 退出应用
        NSApplication.shared.terminate(nil)
    }
    
    deinit {
        if let statusItem = statusItem {
            NSStatusBar.system.removeStatusItem(statusItem)
        }
    }
}

import Combine
