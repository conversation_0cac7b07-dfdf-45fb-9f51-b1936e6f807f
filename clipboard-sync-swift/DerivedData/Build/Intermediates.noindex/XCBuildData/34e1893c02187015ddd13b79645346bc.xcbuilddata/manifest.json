{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync": {"is-mutated": true}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/_CodeSignature", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks>", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o>", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>", "<target-ClipboardSync-****************************************************************--begin-scanning>", "<target-ClipboardSync-****************************************************************--end>", "<target-ClipboardSync-****************************************************************--linker-inputs-ready>", "<target-ClipboardSync-****************************************************************--modules-ready>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>", "<workspace-Release-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList"], "roots": ["/tmp/Yams.dst", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/_CodeSignature", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/PkgInfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist"], "roots": ["/tmp/ClipboardSync.dst", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products"], "outputs": ["<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap"], "roots": ["/tmp/Yams.dst", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Release-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync.xcodeproj", "signature": "27c8447d6e3c093a765c50152e09f300"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks"]}, "P0:::Gate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM-target-ClipboardSync-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync", "<GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-start>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>"]}, "P0:::Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList"], "outputs": ["<target-ClipboardSync-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-ChangePermissions>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-StripSymbols>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-GenerateStubAPI>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-CodeSign>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-Validate>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<Touch /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-CopyAside>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<target-ClipboardSync-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--GeneratedFilesTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ProductStructureTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-ClipboardSync-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap"], "outputs": ["<target-ClipboardSync-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/PkgInfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist"], "outputs": ["<target-ClipboardSync-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--RealityAssetsTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--ModuleMapTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-ClipboardSync-****************************************************************--InfoPlistTaskProducer>", "<target-ClipboardSync-****************************************************************--SanitizerTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-ClipboardSync-****************************************************************--TestTargetTaskProducer>", "<target-ClipboardSync-****************************************************************--TestHostTaskProducer>", "<target-ClipboardSync-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-ClipboardSync-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-ClipboardSync-****************************************************************--DocumentationTaskProducer>", "<target-ClipboardSync-****************************************************************--CustomTaskProducer>", "<target-ClipboardSync-****************************************************************--StubBinaryTaskProducer>", "<target-ClipboardSync-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--start>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources>"], "outputs": ["<target-ClipboardSync-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--HeadermapTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<target-ClipboardSync-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-ClipboardSync-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json"], "outputs": ["<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-ClipboardSync-****************************************************************--generated-headers>"]}, "P0:::Gate target-ClipboardSync-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h"], "outputs": ["<target-ClipboardSync-****************************************************************--swift-generated-headers>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<ExtractAppIntentsMetadata Path(_str: \"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release\") Yams/Metadata.appintents>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-start>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>"]}, "P0:::Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-entry>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-entry": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-entry>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"], "outputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o>"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan"]}, "P0:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan"]}, "P0:target-ClipboardSync-****************************************************************-:Release:CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist/", "<target-ClipboardSync-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync normal>", "<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/_CodeSignature", "<CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<TRIGGER: CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "--compile", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "control-enabled": false, "deps": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "63db1dbec78e78bf3804726061df2086"}, "P0:target-ClipboardSync-****************************************************************-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "--compile", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "control-enabled": false, "deps": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "e0a5c8640468fde7195ddf583b68ba35"}, "P0:target-ClipboardSync-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "deps": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-ClipboardSync-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "ClipboardSync", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "13.0", "--bundle-identifier", "com.clipboardsync.app", "--output", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "--target-triple", "arm64-apple-macos13.0", "--binary-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "--dependency-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "signature": "2b4a0ad1a48056c49a005ddeec37aa43"}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>", "<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/ClipboardSync.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/ClipboardSync.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClipboardSync-****************************************************************--begin-linking>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-scan-inputs-ready>", "<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/ClipboardSync.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--begin-scanning>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--end": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--entry>", "<CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync>", "<GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/PkgInfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<LSRegisterURL /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync Swift Compilation Finished", "<Touch /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/", "<target-ClipboardSync-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-ClipboardSync-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClipboardSync-****************************************************************--Barrier-ChangePermissions>", "<target-ClipboardSync-****************************************************************--Barrier-CodeSign>", "<target-ClipboardSync-****************************************************************--Barrier-CopyAside>", "<target-ClipboardSync-****************************************************************--Barrier-GenerateStubAPI>", "<target-ClipboardSync-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-ClipboardSync-****************************************************************--Barrier-RegisterProduct>", "<target-ClipboardSync-****************************************************************--Barrier-StripSymbols>", "<target-ClipboardSync-****************************************************************--Barrier-Validate>", "<target-ClipboardSync-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-ClipboardSync-****************************************************************--CustomTaskProducer>", "<target-ClipboardSync-****************************************************************--DocumentationTaskProducer>", "<target-ClipboardSync-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-ClipboardSync-****************************************************************--GeneratedFilesTaskProducer>", "<target-ClipboardSync-****************************************************************--HeadermapTaskProducer>", "<target-ClipboardSync-****************************************************************--InfoPlistTaskProducer>", "<target-ClipboardSync-****************************************************************--ModuleMapTaskProducer>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClipboardSync-****************************************************************--ProductStructureTaskProducer>", "<target-ClipboardSync-****************************************************************--RealityAssetsTaskProducer>", "<target-ClipboardSync-****************************************************************--SanitizerTaskProducer>", "<target-ClipboardSync-****************************************************************--StubBinaryTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-ClipboardSync-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-ClipboardSync-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-ClipboardSync-****************************************************************--TestHostTaskProducer>", "<target-ClipboardSync-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-ClipboardSync-****************************************************************--TestTargetTaskProducer>", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClipboardSync-****************************************************************--generated-headers>", "<target-ClipboardSync-****************************************************************--swift-generated-headers>"], "outputs": ["<target-ClipboardSync-****************************************************************--end>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/ClipboardSync.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-ClipboardSync-****************************************************************--begin-compiling>"], "outputs": ["<target-ClipboardSync-****************************************************************--entry>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/ClipboardSync.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClipboardSync-****************************************************************--immediate>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList"], "outputs": ["<target-ClipboardSync-****************************************************************--linker-inputs-ready>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h"], "outputs": ["<target-ClipboardSync-****************************************************************--modules-ready>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "<target-ClipboardSync-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-ClipboardSync-****************************************************************--unsigned-product-ready>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Gate target-ClipboardSync-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-ClipboardSync-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-ClipboardSync-****************************************************************--will-sign>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets/", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "--compile", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx", "--bundle-identifier", "com.clipboardsync.app", "--generate-swift-asset-symbols", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "control-enabled": false, "signature": "1d8e180f2b8df94ca633fba99cd7db69"}, "P0:target-ClipboardSync-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync", "<GenerateDSYMFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM/Contents/Resources/DWARF/ClipboardSync>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "signature": "b5edec76cf66f3aefb11d403d29e34f4"}, "P0:target-ClipboardSync-****************************************************************-:Release:LinkAssetCatalog /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_dependencies"}, "P0:target-ClipboardSync-****************************************************************-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/thinned>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "inputs": ["<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_output/unthinned>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["<target-ClipboardSync-****************************************************************--start>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents", "inputs": ["<target-ClipboardSync-****************************************************************--start>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS", "inputs": ["<target-ClipboardSync-****************************************************************--start>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "inputs": ["<target-ClipboardSync-****************************************************************--start>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources", "<MkDir /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Resources>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/assetcatalog_generated_info.plist", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/PkgInfo"]}, "P0:target-ClipboardSync-****************************************************************-:Release:ProcessProductPackaging /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSync.entitlements /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSync.entitlements /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSync.entitlements", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist", "<target-ClipboardSync-****************************************************************--ProductStructureTaskProducer>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent"]}, "P0:target-ClipboardSync-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "<target-ClipboardSync-****************************************************************--ProductStructureTaskProducer>", "<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "signature": "8d56650d41d8572ab14ea3ea2dd62887"}, "P0:target-ClipboardSync-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "<target-ClipboardSync-****************************************************************--Barrier-CodeSign>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:RegisterWithLaunchServices /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["<target-ClipboardSync-****************************************************************--Barrier-Validate>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:SwiftDriver Compilation ClipboardSync normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation ClipboardSync normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync Swift Compilation Finished"]}, "P0:target-ClipboardSync-****************************************************************-:Release:Touch /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "<target-ClipboardSync-****************************************************************--Barrier-Validate>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "signature": "8e45209df721b61162b14030120ec050"}, "P0:target-ClipboardSync-****************************************************************-:Release:Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/Info.plist", "<target-ClipboardSync-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-ClipboardSync-****************************************************************--will-sign>", "<target-ClipboardSync-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"], "outputs": ["<Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>", "<TRIGGER: Validate /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app>"]}, "P0:target-ClipboardSync-****************************************************************-:Release:ValidateDevelopmentAssets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Preview Content", "<target-ClipboardSync-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build>"], "allow-missing-inputs": true}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>", "<target-Yams-PACKAGE-PRODUCT:Yams-Release-macosx--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-Yams-PACKAGE-PRODUCT:Yams-Release-macosx--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-scan-inputs-ready>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-scan-inputs-ready>", "<target-Yams-PACKAGE-PRODUCT:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-Yams-PACKAGE-PRODUCT:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-Release-macosx--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"]}, "P0:target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"], "outputs": ["<target-Yams-PACKAGE-PRODUCT:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/AliasDereferencingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Anchor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Constructor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Decoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Emitter.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Encoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Mark.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Alias.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Mapping.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Scalar.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Sequence.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Parser.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/RedundancyAliasingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Representer.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Resolver.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/String+Yams.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Tag.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlAnchorProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlError.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlTagProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata Path(_str: \"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release\") Yams/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "Yams", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "10.13", "--bundle-identifier", "yams.Yams", "--output", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.appintents", "--target-triple", "arm64-apple-macos10.13", "--binary-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "--dependency-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "--force", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams", "signature": "d1fe8b5c5477b92a17c42291d4f5a78e"}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>", "<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-scan-inputs-ready>", "<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-scanning>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>", "<ExtractAppIntentsMetadata Path(_str: \"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release\") Yams/Metadata.appintents>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-AppIntentsMetadataTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangeAlternatePermissions>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-ChangePermissions>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CopyAside>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterExecutionPolicyException>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-RegisterProduct>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-StripSymbols>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-Validate>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CopySwiftPackageResourcesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-CustomTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-DocumentationTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-GeneratedFilesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-HeadermapTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-InfoPlistTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleMapTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductPostprocessingTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ProductStructureTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-RealityAssetsTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SanitizerTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-StubBinaryTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftABIBaselineGenerationTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftFrameworkABICheckerTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftPackageCopyFilesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-SwiftStandardLibrariesTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TAPISymbolExtractorTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestHostTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetPostprocessingTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-TestTargetTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-fused-phase0-compile-sources>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-end>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry": {"tool": "phony", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-end>", "<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Yams.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-linker-inputs-ready>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-modules-ready>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<ExtractAppIntentsMetadata Path(_str: \"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release\") Yams/Metadata.appintents>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams Swift Compilation Finished", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-GenerateStubAPI>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Gate target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign": {"tool": "phony", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-unsigned-product-ready>"], "outputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-Barrier-CodeSign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-will-sign>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o>"]}, "P0:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:SwiftDriver Compilation Yams normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Yams normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/AliasDereferencingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Anchor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Constructor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Decoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Emitter.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Encoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Mark.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Alias.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Mapping.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Scalar.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Sequence.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Parser.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/RedundancyAliasingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Representer.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Resolver.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/String+Yams.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Tag.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlAnchorProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlError.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlTagProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams Swift Compilation Finished"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/api.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/emitter.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/parser.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/reader.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o"]}, "P1:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/writer.c", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o.scan", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml"]}, "P2:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o normal", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/api.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/reader.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/scanner.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/writer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos10.13", "-r", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-w", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-L/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-iframework", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-filelist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList", "-nostdlib", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_lto.o", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams", "deps": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml_dependency_info.dat"], "deps-style": "dependency-info", "signature": "63e1bd2d62597fb80e51287c6e149673"}, "P2:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyMetadataFileList"]}, "P2:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/CYaml.DependencyStaticMetadataFileList"]}, "P2:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList", "inputs": ["<target-CYaml-PACKAGE-TARGET:CYaml-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/CYaml.build/Objects-normal/arm64/CYaml.LinkFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo/", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-ClipboardSync-****************************************************************-:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json/", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-ClipboardSync-****************************************************************-:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc/", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-ClipboardSync-****************************************************************-:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule/", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-ClipboardSync-****************************************************************-:Release:Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync normal", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "<target-ClipboardSync-****************************************************************--generated-headers>", "<target-ClipboardSync-****************************************************************--swift-generated-headers>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos13.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-filelist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_lto.o", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "-Wl,-no_warn_duplicate_libraries", "-Wl,-no_warn_duplicate_libraries", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/ClipboardSync.app/Contents/MacOS/ClipboardSync", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "deps": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_dependency_info.dat"], "deps-style": "dependency-info", "signature": "250f7fd8288f0d350bf10ba3e00c9c6e"}, "P2:target-ClipboardSync-****************************************************************-:Release:SwiftDriver Compilation Requirements ClipboardSync normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements ClipboardSync normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync-8ec61d61a30072d1b8f40e2f230fc9df-VFS/all-product-headers.yaml", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-ClipboardSync-****************************************************************--copy-headers-completion>", "<target-ClipboardSync-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.swiftdoc"]}, "P2:target-ClipboardSync-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-Swift.h", "<target-ClipboardSync-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/ClipboardSync-Swift.h"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-non-framework-target-headers.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-all-target-headers.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-generated-files.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-own-target-headers.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync-project-headers.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyMetadataFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.DependencyStaticMetadataFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/ClipboardSync.hmap"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/Entitlements.plist"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-OutputFileMap.json"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.LinkFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftConstValuesFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync.SwiftFileList"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync_const_extract_protocols.json"]}, "P2:target-ClipboardSync-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist", "inputs": ["<target-ClipboardSync-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/empty-ClipboardSync.plist"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap/", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo/", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json/", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc/", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule/", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o normal", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-generated-headers>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-swift-generated-headers>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-linking>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o", "<Linked Binary /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos10.13", "-r", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-w", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-L/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-L/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/PackageFrameworks", "-F/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release", "-iframework", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-filelist", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "-nostdlib", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_lto.o", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "-o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o"], "env": {}, "working-directory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams", "deps": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_dependency_info.dat"], "deps-style": "dependency-info", "signature": "80bcff1aae306a850852164d1899d24d"}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:SwiftDriver Compilation Requirements Yams normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Yams normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/AliasDereferencingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Anchor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Constructor.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Decoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Emitter.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Encoder.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Mark.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Alias.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Mapping.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Scalar.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Sequence.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Parser.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/RedundancyAliasingStrategy.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Representer.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Resolver.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/String+Yams.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Tag.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlAnchorProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlError.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlTagProviding.swift", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams.modulemap", "<ClangStatCache /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-copy-headers-completion>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-ModuleVerifierTaskProducer>", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "inputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h", "<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps/Yams-Swift.h"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-OutputFileMap.json"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.LinkFileList"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftConstValuesFileList"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.SwiftFileList"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams_const_extract_protocols.json"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyMetadataFileList"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.DependencyStaticMetadataFileList"]}, "P2:target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos:Release:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap", "inputs": ["<target-Yams-PACKAGE-TARGET:Yams-SDKROOT:macosx:SDK_VARIANT:macos-immediate>"], "outputs": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Yams.modulemap"]}}}