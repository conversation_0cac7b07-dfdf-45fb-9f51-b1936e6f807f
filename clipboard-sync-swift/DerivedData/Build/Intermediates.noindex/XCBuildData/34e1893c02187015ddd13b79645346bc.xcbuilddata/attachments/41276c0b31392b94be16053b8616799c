{"": {"const-values": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.d", "diagnostics": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSync-master.swiftdeps"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}}