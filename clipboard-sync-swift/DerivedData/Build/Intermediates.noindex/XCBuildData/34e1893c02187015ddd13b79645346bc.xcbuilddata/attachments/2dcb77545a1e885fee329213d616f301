/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ContentView.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardManager.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/NetworkManager.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigManager.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusBarManager.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ConfigView.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/StatusView.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/LogView.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/ClipboardSyncApp.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/Objects-normal/arm64/GeneratedAssetSymbols.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/Yams.o
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Products/Release/CYaml.o
