/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift
/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift
