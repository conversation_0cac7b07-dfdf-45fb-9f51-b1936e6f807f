{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "CYaml", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_INFOPLIST_FILE": "YES", "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "", "MODULEMAP_PATH": "", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.CYaml", "PRODUCT_MODULE_NAME": "CYaml", "PRODUCT_NAME": "CYaml", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "CYaml"}, "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "CYaml", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_INFOPLIST_FILE": "YES", "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "", "MODULEMAP_PATH": "", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.CYaml", "PRODUCT_MODULE_NAME": "CYaml", "PRODUCT_NAME": "CYaml", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "CYaml"}, "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_0", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_1", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_2", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_3", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_4", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_5", "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [], "guid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic", "name": "CYaml", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic", "name": "CYaml.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}