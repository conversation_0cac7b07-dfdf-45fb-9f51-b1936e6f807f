{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "ClipboardSync/ClipboardSync.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"ClipboardSync/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_CFBundleDisplayName": "Clipboard Sync", "INFOPLIST_KEY_LSApplicationCategoryType": "public.app-category.utilities", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.clipboardsync.app", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "8ec61d61a30072d1b8f40e2f230fc9df712cf6155e68705403a3ce347a5c073b", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "ClipboardSync/ClipboardSync.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"ClipboardSync/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_CFBundleDisplayName": "Clipboard Sync", "INFOPLIST_KEY_LSApplicationCategoryType": "public.app-category.utilities", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.clipboardsync.app", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "8ec61d61a30072d1b8f40e2f230fc9df2535383eff43d47c5c538a6426a144bf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "8ec61d61a30072d1b8f40e2f230fc9df61b4e108bf5cda08b76fbf4321f47b84", "guid": "8ec61d61a30072d1b8f40e2f230fc9df298b4872d2758bdc9e648efb81acef33"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dfe6a82360ced20ce9269f794149b71bca", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfbc2e24b55415222a77366d0b3ef408e1"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9df07466a103f247da3b87df430973c4cd6", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfc04c6455ac481baf9374bcd20a21f375"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9df4b06362e101f879df6f5e9a628382bf7", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfd669b8efd0e697b260d5a1beab574591"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dff67cf28c6e07e9f4ec2214b26d44251e", "guid": "8ec61d61a30072d1b8f40e2f230fc9df551853e703d1103198b9b8a7c3a762d2"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dfc62374baed8f724d9f09a86885b81452", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfb9c446393d42ec94421a3ba6ddb6b347"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9df71b627c473aa39dcb9cb9619ea7d2ae0", "guid": "8ec61d61a30072d1b8f40e2f230fc9df464c8cbeacd4bbe9cb95c057e944fcc5"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dfaa6cf63aeab406307c9f3643996e9df0", "guid": "8ec61d61a30072d1b8f40e2f230fc9df815d29b1cf09950da25afecae81e340e"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dfbac30461c8dd798432b328995ff0bde3", "guid": "8ec61d61a30072d1b8f40e2f230fc9df1c2f0cfa208430b6543e88997fbe9fdb"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9dfbf3237178f916bcee9b393154dde17d7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "eac3e3d9cda98e7bface4e07a459e8e0", "targetReference": "PACKAGE-PRODUCT:Yams"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9dfaac547c87381119aab64d7470375a5f0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "8ec61d61a30072d1b8f40e2f230fc9dfd637ce4ff598f4011ac19c6cd49dca17", "guid": "8ec61d61a30072d1b8f40e2f230fc9df5932918b1c47b1fef4ad28f0d862115c"}, {"fileReference": "8ec61d61a30072d1b8f40e2f230fc9df42ed34e4442f2b1eb352b4c87a4f7f19", "guid": "8ec61d61a30072d1b8f40e2f230fc9df5eb7ed1d84d92b2abe31d3cbfb32ad7e"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9dfa5378dfe22dc6e65b13ad28f0cd50f6b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:Yams", "name": "Yams"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9df9c7620cf0580083a8fb7fb62364b7599", "name": "ClipboardSync", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "8ec61d61a30072d1b8f40e2f230fc9df5fdab66972ec88a65e8af795ecc91b6c", "name": "ClipboardSync.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}