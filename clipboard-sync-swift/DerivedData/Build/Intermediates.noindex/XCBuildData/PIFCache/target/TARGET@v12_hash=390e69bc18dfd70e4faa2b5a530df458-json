{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "CYaml.o", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_MASTER_OBJECT_FILE": "NO", "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "", "MODULEMAP_PATH": "", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.CYaml", "PRODUCT_MODULE_NAME": "CYaml", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "CYaml", "TARGET_NAME": "CYaml"}, "guid": "PACKAGE-TARGET:CYaml::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "CYaml.o", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_MASTER_OBJECT_FILE": "NO", "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "", "MODULEMAP_PATH": "", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.CYaml", "PRODUCT_MODULE_NAME": "CYaml", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "CYaml", "TARGET_NAME": "CYaml"}, "guid": "PACKAGE-TARGET:CYaml::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "HEADER_SEARCH_PATHS": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_0", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_1", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_2", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_3", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_4", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_5", "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:CYaml::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [], "dynamicTargetVariantGuid": "PACKAGE-TARGET:CYaml-B6BE09F7C3B-dynamic", "guid": "PACKAGE-TARGET:CYaml", "name": "CYaml", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:CYaml", "name": "CYaml.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}