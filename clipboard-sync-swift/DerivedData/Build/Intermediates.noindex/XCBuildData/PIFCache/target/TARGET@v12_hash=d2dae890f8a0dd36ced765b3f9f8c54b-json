{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "Yams", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module Yams {\nheader \"Yams-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/Yams.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.Yams", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "Yams-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "Yams"}, "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/Yams.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "Yams", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "YAML_DECLARE_STATIC"], "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module Yams {\nheader \"Yams-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/Yams.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "yams.Yams", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "Yams-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "Yams"}, "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/Yams.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_0", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_1", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_2", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_3", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_4", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_5", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_6", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_7", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_8", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_9", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_10", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_11", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::11", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_12", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::12", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_13", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::13", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_14", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::14", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_15", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::15", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_16", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::16", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_17", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::17", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_18", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::18", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_19", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::19", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_20", "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0::20", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:CYaml"}], "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:CYaml", "platformFilters": []}], "guid": "PACKAGE-TARGET:Yams-712EB057E-dynamic", "name": "Yams", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:Yams-712EB057E-dynamic", "name": "Yams.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}