{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "8ec61d61a30072d1b8f40e2f230fc9df4ebad8cf192278f35e9a1947c5e1a916", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "8ec61d61a30072d1b8f40e2f230fc9dfae68a07ae45808cf3682ce342e87ed94", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfbac30461c8dd798432b328995ff0bde3", "path": "ClipboardSyncApp.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9df61b4e108bf5cda08b76fbf4321f47b84", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfc62374baed8f724d9f09a86885b81452", "path": "ConfigView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9df71b627c473aa39dcb9cb9619ea7d2ae0", "path": "StatusView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfaa6cf63aeab406307c9f3643996e9df0", "path": "LogView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9dff1502fd791cb394ca4d11fc381da6324", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfe6a82360ced20ce9269f794149b71bca", "path": "ClipboardManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9df07466a103f247da3b87df430973c4cd6", "path": "NetworkManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9df4b06362e101f879df6f5e9a628382bf7", "path": "ConfigManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "8ec61d61a30072d1b8f40e2f230fc9dff67cf28c6e07e9f4ec2214b26d44251e", "path": "StatusBarManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9df0eecf75bf8c8defc829e0357021d18c0", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "8ec61d61a30072d1b8f40e2f230fc9df42ed34e4442f2b1eb352b4c87a4f7f19", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "8ec61d61a30072d1b8f40e2f230fc9df4519c61a2489776264ea67cf43e5ff15", "path": "ClipboardSync.entitlements", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "8ec61d61a30072d1b8f40e2f230fc9dfd637ce4ff598f4011ac19c6cd49dca17", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9dfd8de01503fb41672f06cc53f505ba15a", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9df40d770574c0b378eabef7ea4eaa584f8", "name": "ClipboardSync", "path": "ClipboardSync", "sourceTree": "<group>", "type": "group"}, {"guid": "8ec61d61a30072d1b8f40e2f230fc9dff024e073ae01f5811fad339d6a95f329", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "8ec61d61a30072d1b8f40e2f230fc9df44823b867a07683f8ee44f327b2e00d3", "name": "ClipboardSync", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "8ec61d61a30072d1b8f40e2f230fc9df", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift", "targets": ["TARGET@v11_hash=dd9180cb77422c1c9c37184205737522"]}