{"buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/jpsim/Yams.git::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "s", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/jpsim/Yams.git::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "defaultConfigurationName": "Release", "groupTree": {"children": [{"children": [], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_0", "name": "AdditionalFiles", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_1", "name": "Binaries", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_0", "path": "src/api.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_1", "path": "src/emitter.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_2", "path": "src/parser.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_3", "path": "src/reader.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_4", "path": "src/scanner.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2::REF_5", "path": "src/writer.c", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_2", "name": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_0", "path": "src/api.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_1", "path": "src/emitter.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_2", "path": "src/parser.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_3", "path": "src/reader.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_4", "path": "src/scanner.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3::REF_5", "path": "src/writer.c", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_3", "name": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_0", "path": "AliasDereferencingStrategy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_1", "path": "Anchor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_2", "path": "Constructor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_3", "path": "Decoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_4", "path": "Emitter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_5", "path": "Encoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_6", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_7", "path": "Node.Alias.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_8", "path": "Node.Mapping.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_9", "path": "Node.Scalar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_10", "path": "Node.Sequence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_11", "path": "Node.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_12", "path": "Parser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_13", "path": "RedundancyAliasingStrategy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_14", "path": "Representer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_15", "path": "Resolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_16", "path": "String+Yams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_17", "path": "Tag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_18", "path": "YamlAnchorProviding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_19", "path": "YamlError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4::REF_20", "path": "YamlTagProviding.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_4", "name": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_0", "path": "AliasDereferencingStrategy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_1", "path": "Anchor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_2", "path": "Constructor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_3", "path": "Decoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_4", "path": "Emitter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_5", "path": "Encoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_6", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_7", "path": "Node.Alias.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_8", "path": "Node.Mapping.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_9", "path": "Node.Scalar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_10", "path": "Node.Sequence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_11", "path": "Node.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_12", "path": "Parser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_13", "path": "RedundancyAliasingStrategy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_14", "path": "Representer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_15", "path": "Resolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_16", "path": "String+Yams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_17", "path": "Tag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_18", "path": "YamlAnchorProviding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_19", "path": "YamlError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5::REF_20", "path": "YamlTagProviding.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP::REF_5", "name": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams", "sourceTree": "<absolute>", "type": "group"}], "guid": "PACKAGE:https://github.com/jpsim/Yams.git::MAINGROUP", "name": "", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "PACKAGE:https://github.com/jpsim/Yams.git", "path": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Package.swift", "projectDirectory": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams", "projectIsPackage": "true", "projectName": "Yams", "targets": ["TARGET@v12_hash=fb0969bd09dda1219d48fe0a2a19575d", "TARGET@v12_hash=bbefddea33749a6470f702ad9985affb", "TARGET@v12_hash=390e69bc18dfd70e4faa2b5a530df458", "TARGET@v12_hash=317868128c5e3a065fb5a3dc2629c22b", "TARGET@v12_hash=9460c7a9f6f19956a8a6376d06ecdec5", "TARGET@v12_hash=d2dae890f8a0dd36ced765b3f9f8c54b"]}