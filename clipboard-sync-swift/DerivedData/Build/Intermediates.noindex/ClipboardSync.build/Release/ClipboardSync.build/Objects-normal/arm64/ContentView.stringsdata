{"source": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift", "tables": {"Localizable": [{"comment": "", "key": "Tab", "location": {"startingColumn": 20, "startingLine": 38}}, {"comment": "", "key": "Clipboard Sync", "location": {"startingColumn": 22, "startingLine": 15}}, {"comment": "", "key": "Running", "location": {"startingColumn": 55, "startingLine": 27}}, {"comment": "", "key": "Stopped", "location": {"startingColumn": 67, "startingLine": 27}}, {"comment": "", "key": "Configuration", "location": {"startingColumn": 22, "startingLine": 39}}, {"comment": "", "key": "Status", "location": {"startingColumn": 22, "startingLine": 40}}, {"comment": "", "key": "Logs", "location": {"startingColumn": 22, "startingLine": 41}}]}, "version": 1}