version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "90c51f3a305256185c46ba71ab220e00455ab51142fd9140a94769063f40b0dd"
build_start_time: [1751615608, 764931000]
build_end_time: [1751615613, 115219000]
inputs:
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/ClipboardSyncApp.swift": [1751613779, 638726889]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ClipboardManager.swift": [1751613562, 988526287]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/ConfigManager.swift": [1751613649, 316753138]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/NetworkManager.swift": [1751615421, 539388672]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Managers/StatusBarManager.swift": [1751615517, 154446857]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift": [1751613494, 710213695]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ContentView.swift": [1751615440, 500997603]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/LogView.swift": [1751613531, 292445814]
  "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/StatusView.swift": [1751613513, 248253651]
  ? "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/ClipboardSync.build/Release/ClipboardSync.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1751615608, 760637670]
