{"source": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/ClipboardSync/Views/ConfigView.swift", "tables": {"Localizable": [{"comment": "", "key": "Configuration", "location": {"startingColumn": 16, "startingLine": 129}}, {"comment": "", "key": "Sender <PERSON>s", "location": {"startingColumn": 26, "startingLine": 12}}, {"comment": "", "key": "Receiver Settings", "location": {"startingColumn": 26, "startingLine": 54}}, {"comment": "", "key": "Advanced Settings", "location": {"startingColumn": 26, "startingLine": 98}}, {"comment": "", "key": "Enable Sender", "location": {"startingColumn": 32, "startingLine": 14}}, {"comment": "", "key": "Ntfy Topic URL:", "location": {"startingColumn": 34, "startingLine": 17}}, {"comment": "", "key": "https://ntfy.sh/your_topic_here", "location": {"startingColumn": 39, "startingLine": 20}}, {"comment": "", "key": "Poll Interval (seconds):", "location": {"startingColumn": 38, "startingLine": 26}}, {"comment": "", "key": "1.0", "location": {"startingColumn": 43, "startingLine": 29}}, {"comment": "", "key": "Request Timeout (seconds):", "location": {"startingColumn": 38, "startingLine": 34}}, {"comment": "", "key": "15", "location": {"startingColumn": 43, "startingLine": 37}}, {"comment": "", "key": "Filename Prefix:", "location": {"startingColumn": 34, "startingLine": 43}}, {"comment": "", "key": "clipboard_content_", "location": {"startingColumn": 39, "startingLine": 46}}, {"comment": "", "key": "Enable Receiver", "location": {"startingColumn": 32, "startingLine": 56}}, {"comment": "", "key": "Ntfy Server:", "location": {"startingColumn": 38, "startingLine": 60}}, {"comment": "", "key": "ntfy.sh", "location": {"startingColumn": 43, "startingLine": 63}}, {"comment": "", "key": "Ntfy Topic:", "location": {"startingColumn": 38, "startingLine": 68}}, {"comment": "", "key": "your_receive_topic_here", "location": {"startingColumn": 43, "startingLine": 71}}, {"comment": "", "key": "Reconnect Delay (seconds):", "location": {"startingColumn": 38, "startingLine": 78}}, {"comment": "", "key": "5", "location": {"startingColumn": 43, "startingLine": 81}}, {"comment": "", "key": "Request Timeout (seconds):", "location": {"startingColumn": 38, "startingLine": 86}}, {"comment": "", "key": "15", "location": {"startingColumn": 43, "startingLine": 89}}, {"comment": "", "key": "Log Level:", "location": {"startingColumn": 32, "startingLine": 100}}, {"comment": "", "key": "Enable macOS Image Support", "location": {"startingColumn": 32, "startingLine": 108}}, {"comment": "", "key": "Debug", "location": {"startingColumn": 34, "startingLine": 101}}, {"comment": "", "key": "Info", "location": {"startingColumn": 34, "startingLine": 102}}, {"comment": "", "key": "Warning", "location": {"startingColumn": 34, "startingLine": 103}}, {"comment": "", "key": "Error", "location": {"startingColumn": 34, "startingLine": 104}}, {"comment": "", "key": "Load from YAML", "location": {"startingColumn": 28, "startingLine": 115}}, {"comment": "", "key": "Save Configuration", "location": {"startingColumn": 28, "startingLine": 121}}, {"comment": "", "key": "OK", "location": {"startingColumn": 20, "startingLine": 130}}]}, "version": 1}