dependencies: \
  /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include/module.modulemap \
  /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/scanner.c \
  /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/src/yaml_private.h \
  /Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/CYaml/include/yaml.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c_standard_library.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/DarwinFoundation.modulemap \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap
