{"": {"const-values": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.d", "diagnostics": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftdeps"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/AliasDereferencingStrategy.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/AliasDereferencingStrategy.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Anchor.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Anchor.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Constructor.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Constructor.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Decoder.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Decoder.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Emitter.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Emitter.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Encoder.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Encoder.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Mark.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Mark.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Alias.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Alias.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Mapping.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Mapping.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Scalar.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Scalar.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.Sequence.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.Sequence.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Node.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Node.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Parser.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Parser.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/RedundancyAliasingStrategy.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/RedundancyAliasingStrategy.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Representer.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Representer.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Resolver.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Resolver.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/String+Yams.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/String+Yams.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/Tag.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Tag.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlAnchorProviding.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlAnchorProviding.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlError.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlError.o"}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/YamlTagProviding.swift": {"index-unit-output-path": "/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o", "llvm-bc": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.bc", "object": "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/YamlTagProviding.o"}}