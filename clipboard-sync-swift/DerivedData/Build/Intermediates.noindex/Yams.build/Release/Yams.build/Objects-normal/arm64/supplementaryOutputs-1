"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/SourcePackages/checkouts/Yams/Sources/Yams/AliasDereferencingStrategy.swift":
  swiftsourceinfo: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftsourceinfo"
  const-values: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.swiftconstvalues"
  objc-header: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-Swift.h"
  swiftdoc: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftdoc"
  abi-baseline-json: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.abi.json"
  swiftmodule: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams.swiftmodule"
  dependencies: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.d"
  diagnostics: "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/clipboard-sync-swift/DerivedData/Build/Intermediates.noindex/Yams.build/Release/Yams.build/Objects-normal/arm64/Yams-master.dia"
