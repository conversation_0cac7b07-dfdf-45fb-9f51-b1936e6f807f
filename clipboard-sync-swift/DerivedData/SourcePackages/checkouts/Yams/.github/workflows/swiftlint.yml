name: SwiftLint

on:
  pull_request:
    paths:
      - '.github/workflows/swiftlint.yml'
      - '.swiftlint.yml'
      - '**/*.swift'

concurrency:
  group: swiftlint-${{ github.ref }}
  cancel-in-progress: true

jobs:
  SwiftLint:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/realm/swiftlint:0.54.0
    steps:
      - uses: actions/checkout@v4
      - name: SwiftLint
        run: swiftlint lint --strict
