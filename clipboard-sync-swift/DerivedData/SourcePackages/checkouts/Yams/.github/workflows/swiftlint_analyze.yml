name: SwiftLint Analyze

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/swiftlint_analyze.yml'
      - 'Yams.xcodeproj/**'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Tests/**/*.swift'
      - '!Tests/LinuxMain.swift'
  pull_request:
    paths:
      - '.github/workflows/swiftlint_analyze.yml'
      - 'Yams.xcodeproj/**'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Tests/**/*.swift'
      - '!Tests/LinuxMain.swift'

concurrency:
  group: swiftlint-analyze-${{ github.ref }}
  cancel-in-progress: true

jobs:
  Analyze:
    runs-on: macos-14
    env:
      DEVELOPER_DIR: /Applications/Xcode_15.4.app
    steps:
      - uses: actions/checkout@v4
      - name: Generate xcodebuild.log
        run: xcodebuild -sdk macosx -scheme Yams -project Yams.xcodeproj clean build-for-testing > xcodebuild.log
        shell: bash
      - name: Install SwiftLint
        run: brew install swiftlint || brew upgrade swiftlint
      - name: Run SwiftLint Analyze
        run: swiftlint analyze --strict --compiler-log-path xcodebuild.log --reporter github-actions-logging
