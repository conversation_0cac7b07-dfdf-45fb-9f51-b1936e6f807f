name: Bazel

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/bazel.yml'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'
      - '**/BUILD'
      - 'MODULE.bazel'
      - 'WORKSPACE'
      - '.bazelrc'
      - '.bazelversion'
  pull_request:
    paths:
      - '.github/workflows/bazel.yml'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'
      - '**/BUILD'
      - 'MODULE.bazel'
      - 'WORKSPACE'
      - '.bazelrc'
      - '.bazelversion'

concurrency:
  group: bazel-${{ github.ref }}
  cancel-in-progress: true

jobs:
  macOS:
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v4
      - name: Apple tests
        run: bazelisk test //Tests/...
  Linux:
    strategy:
      matrix:
        tag: ['5.7', '5.8', '5.9', '5.10']
    runs-on: ubuntu-latest
    container:
      image: swift:${{ matrix.tag }}-focal
    steps:
      - uses: actions/checkout@v4
      - uses: bazelbuild/setup-bazelisk@v3
      - name: Yams tests
        run: bazel test --test_output=all //Tests:UnitTests
