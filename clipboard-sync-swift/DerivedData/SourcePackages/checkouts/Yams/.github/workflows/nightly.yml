name: Nightly

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/nightly.yml'
      - 'Package*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Sources/**/module.modulemap'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'
  pull_request:
    paths:
      - '.github/workflows/nightly.yml'
      - 'Package*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Sources/**/module.modulemap'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'
  schedule:
    - cron: '0 4 * * *'

concurrency:
  group: nightly-${{ github.ref }}
  cancel-in-progress: true

jobs:
  Nightly:
    runs-on: ubuntu-latest
    container:
      image: swiftlang/swift:nightly
    steps:
      - uses: actions/checkout@v4
      - run: swift --version
      - run: YAMS_DEFAULT_ENCODING=UTF16 swift test --parallel
      - run: YAMS_DEFAULT_ENCODING=UTF8 swift test --parallel
