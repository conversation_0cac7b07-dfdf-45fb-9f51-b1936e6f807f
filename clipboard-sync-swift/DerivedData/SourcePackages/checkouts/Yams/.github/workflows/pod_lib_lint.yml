name: pod lib lint

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/pod_lib_lint.yml'
      - '*.podspec'
      - 'Gemfile*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
  pull_request:
    paths:
      - '.github/workflows/pod_lib_lint.yml'
      - '*.podspec'
      - 'Gemfile*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'

concurrency:
  group: pod-lib-lint-${{ github.ref }}
  cancel-in-progress: true

jobs:
  pod_lib_lint:
    name: pod lib lint
    runs-on: macos-14
    env:
      DEVELOPER_DIR: /Applications/Xcode_15.4.app
    strategy:
      matrix:
        platform: [macOS, iOS, tvOS, visionOS]
    steps:
      - uses: actions/checkout@v4
      - run: bundle install --path vendor/bundle
      - if: matrix.platform == 'visionOS'
        run: xcodebuild -downloadPlatform visionOS
      - run: bundle exec pod lib lint --platforms=${{ matrix.platform }} --verbose
