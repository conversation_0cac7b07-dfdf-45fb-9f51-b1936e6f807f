name: Jazzy

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/jazzy.yml'
      - '.jazzy.yaml'
      - '**/*.md'
      - '**/*.jpg'
      - 'Gemfile*'
      - 'Package*'
      - 'Sources/**/*.swift'
  pull_request:
    paths:
      - '.github/workflows/jazzy.yml'
      - '.jazzy.yaml'
      - '**/*.md'
      - '**/*.jpg'
      - 'Gemfile*'
      - 'Package*'
      - 'Sources/**/*.swift'

concurrency:
  group: jazzy-${{ github.ref }}
  cancel-in-progress: true

jobs:
  Jazzy:
    runs-on: macos-14
    env:
      DEVELOPER_DIR: /Applications/Xcode_15.4.app
    steps:
      - uses: actions/checkout@v4
      - name: Install SourceKitten
        run: brew install sourcekitten
      - run: swift build
      - name: Generate documentation json
        run: sourcekitten doc --spm --module-name Yams > yams.json
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3.3
          bundler-cache: true
      - name: Run jazzy
        run: bundle exec jazzy --clean --sourcekitten-sourcefile yams.json
      - name: Validate documentation coverage
        run: |
          if ruby -rjson -e "j = JSON.parse(File.read('docs/undocumented.json')); exit j['warnings'].length != 0"; then
            echo "Undocumented declarations:"
            cat docs/undocumented.json
            exit 1
          fi
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: API Docs
          path: docs
      - name: Push to gh-pages
        if: github.event_name == 'push'
        run: |
          git config --global user.email "${GITHUB_ACTOR}"
          git config --global user.name "${GITHUB_ACTOR}@users.noreply.github.com"
          git clone "https://x-access-token:${GITHUB_TOKEN}@github.com/${GITHUB_REPOSITORY}.git" out

          cd out
          git checkout gh-pages
          git rm -rf .
          cd ..

          cp -a docs/. out/.
          cd out

          git add -A
          git commit -m "Automated deployment to GitHub Pages: ${GITHUB_SHA}" --allow-empty

          git push origin gh-pages
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
