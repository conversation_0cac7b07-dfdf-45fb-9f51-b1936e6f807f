name: SwiftPM

on:
  push:
    branches: [main]
    paths:
      - '.codecov.yml'
      - '.github/workflows/swiftpm.yml'
      - 'Package*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Sources/**/module.modulemap'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'
  pull_request:
    paths:
      - '.codecov.yml'
      - '.github/workflows/swiftpm.yml'
      - 'Package*'
      - 'Sources/**/*.[ch]'
      - 'Sources/**/*.swift'
      - 'Sources/**/module.modulemap'
      - 'Tests/**/*.swift'
      - 'Tests/**/*.ya?ml'

concurrency:
  group: swiftpm-${{ github.ref }}
  cancel-in-progress: true

jobs:
  Xcode_Ventura:
    name: macOS 13 with Xcode ${{ matrix.xcode_version }}
    strategy:
      matrix:
        xcode_version: ['14.3', '15.0']
    runs-on: macos-13
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.xcode_version }}.app
    steps:
      - uses: actions/checkout@v4
      - run: swift -version
      - run: YAMS_DEFAULT_ENCODING=UTF16 swift test --parallel
      - run: YAMS_DEFAULT_ENCODING=UTF8 swift test --parallel

  Xcode_Sonoma:
    name: macOS 14 with Xcode ${{ matrix.xcode_version }}
    strategy:
      matrix:
        xcode_version: ['15.4']
    runs-on: macos-14
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.xcode_version }}.app
    steps:
      - uses: actions/checkout@v4
      - run: swift -version
      - run: YAMS_DEFAULT_ENCODING=UTF16 swift test --parallel
      - run: YAMS_DEFAULT_ENCODING=UTF8 swift test --parallel
      - name: Code Coverage
        if: matrix.xcode_version == '15.4'
        run: |
          swift test --enable-code-coverage
          xcrun llvm-cov export -format="lcov" .build/debug/YamsPackageTests.xctest/Contents/MacOS/YamsPackageTests -instr-profile .build/debug/codecov/default.profdata > coverage.lcov
          if [[ -n "${CODECOV_TOKEN}" ]]; then
            bash <(curl -s https://codecov.io/bash) -f coverage.lcov
          fi
        env: { 'CODECOV_TOKEN': '${{ secrets.CODECOV_TOKEN }}' }

  Xcode_Sequoia:
    name: macOS 15 with Xcode ${{ matrix.xcode_version }}
    strategy:
      matrix:
        xcode_version: ['15.4', '16.0', '16.2', '16.3']
    runs-on: macos-15
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.xcode_version }}.app
    steps:
      - uses: actions/checkout@v4
      - run: swift -version
      - run: YAMS_DEFAULT_ENCODING=UTF16 swift test --parallel
      - run: YAMS_DEFAULT_ENCODING=UTF8 swift test --parallel
      - name: Code Coverage
        if: matrix.xcode_version == '15.4'
        run: |
          swift test --enable-code-coverage
          xcrun llvm-cov export -format="lcov" .build/debug/YamsPackageTests.xctest/Contents/MacOS/YamsPackageTests -instr-profile .build/debug/codecov/default.profdata > coverage.lcov
          if [[ -n "${CODECOV_TOKEN}" ]]; then
            bash <(curl -s https://codecov.io/bash) -f coverage.lcov
          fi
        env: { 'CODECOV_TOKEN': '${{ secrets.CODECOV_TOKEN }}' }

  Linux:
    name: Linux with Swift ${{ matrix.tag }}
    strategy:
      matrix:
        tag: ['5.7', '5.8', '5.9', '5.10', '6.0', '6.1']
    runs-on: ubuntu-latest
    container:
      image: swift:${{ matrix.tag }}
    steps:
      - uses: actions/checkout@v4
      - run: YAMS_DEFAULT_ENCODING=UTF16 swift test --parallel
      - run: YAMS_DEFAULT_ENCODING=UTF8 swift test --parallel

  Windows:
    name: Windows with Swift ${{ matrix.swift_version }}
    runs-on: windows-latest
    strategy:
      matrix:
        swift_version: ['5.8.1', '5.9.1', '5.10.1', '6.0', '6.1']
    steps:
      - uses: actions/checkout@v4
      - uses: SwiftyLab/setup-swift@bf0987b4ed5eca4d558dda243b257f0562b4b36e
        with:
          swift-version: ${{ matrix.swift_version }}
      - name: Build
        run: swift build -v
      - name: Test
        run: swift test -v
  Android:
    name: Android with Swift ${{ matrix.tag }}
    strategy:
      matrix:
        tag: ['6.0.3']
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: skiptools/swift-android-action@v2
        with:
          swift-version: ${{ matrix.tag }}
          # needed for tests that use fixturesDirectory
          copy-files: Tests
          test-env: TEST_WORKSPACE=1

