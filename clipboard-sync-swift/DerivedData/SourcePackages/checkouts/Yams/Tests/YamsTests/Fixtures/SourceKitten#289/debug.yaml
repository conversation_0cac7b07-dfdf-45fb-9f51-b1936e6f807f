client:
  name: swift-build
tools: {}
targets:
  "test": ["<Result.module>","<Nimble.module>","<Quick.module>","<Commandant.module>","<SWXMLHash.module>","<Yaml.module>","<sourcekitten.module>","<SourceKittenFramework.module>","<SourceKittenFrameworkTests.module>","<sourcekitten.exe>","<SourceKittenPackageTests.test>"]
  "main": ["<Result.module>","<Nimble.module>","<Quick.module>","<Commandant.module>","<SWXMLHash.module>","<Yaml.module>","<sourcekitten.module>","<SourceKittenFramework.module>","<sourcekitten.exe>"]
default: "main"
commands: 
  "<Result.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "Result"
    module-output-path: "/SourceKitten/.build/debug/Result.swiftmodule"
    inputs: []
    outputs: ["<Result.module>","/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/Result.build"
    objects: ["/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/Result-3.0.0/Result/Result.swift","/SourceKitten/Packages/Result-3.0.0/Result/ResultProtocol.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<Nimble.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "Nimble"
    module-output-path: "/SourceKitten/.build/debug/Nimble.swiftmodule"
    inputs: []
    outputs: ["<Nimble.module>","/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/Nimble.build"
    objects: ["/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/DSL+Wait.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/DSL.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Expectation.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Expression.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/FailureMessage.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/AdapterProtocols.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/AssertionDispatcher.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/AssertionRecorder.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/NimbleEnvironment.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/NimbleXCTestHandler.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/NMBExpectation.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/NMBObjCMatcher.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/AllPass.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/AsyncMatcherWrapper.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeAKindOf.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeAnInstanceOf.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeCloseTo.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeEmpty.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeginWith.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeGreaterThan.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeGreaterThanOrEqualTo.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeIdenticalTo.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeLessThan.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeLessThanOrEqual.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeLogical.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeNil.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/BeVoid.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/Contain.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/EndWith.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/Equal.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/HaveCount.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/Match.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/MatcherFunc.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/MatcherProtocols.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/MatchError.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/PostNotification.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/RaisesException.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/SatisfyAnyOf.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Matchers/ThrowError.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Utils/Async.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Utils/Errors.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Utils/Functional.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Utils/SourceLocation.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Utils/Stringers.swift","/SourceKitten/Packages/Nimble-5.0.0/Sources/Nimble/Adapters/NonObjectiveC/ExceptionCapture.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<Quick.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "Quick"
    module-output-path: "/SourceKitten/.build/debug/Quick.swiftmodule"
    inputs: ["<Nimble.module>"]
    outputs: ["<Quick.module>","/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/Quick.build"
    objects: ["/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Callsite.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/ErrorUtility.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Example.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/ExampleGroup.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/ExampleMetadata.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Filter.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/NSBundle+CurrentTestBundle.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/QuickMain.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/QuickSelectedTestSuiteBuilder.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/QuickSpec.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/QuickTestSuite.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/String+FileName.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/World.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Configuration/Configuration.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Configuration/QuickConfiguration.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/DSL/DSL.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/DSL/World+DSL.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Hooks/Closures.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Hooks/ExampleHooks.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Hooks/HooksPhase.swift","/SourceKitten/Packages/Quick-0.10.0/Sources/Quick/Hooks/SuiteHooks.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<Commandant.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "Commandant"
    module-output-path: "/SourceKitten/.build/debug/Commandant.swiftmodule"
    inputs: ["<Result.module>","<Nimble.module>","<Quick.module>"]
    outputs: ["<Commandant.module>","/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/Commandant.build"
    objects: ["/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/Argument.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/ArgumentParser.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/ArgumentProtocol.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/Command.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/Errors.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/HelpCommand.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/LinuxSupport.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/Option.swift","/SourceKitten/Packages/Commandant-0.11.2/Sources/Commandant/Switch.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<SWXMLHash.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "SWXMLHash"
    module-output-path: "/SourceKitten/.build/debug/SWXMLHash.swiftmodule"
    inputs: []
    outputs: ["<SWXMLHash.module>","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/SWXMLHash.build"
    objects: ["/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/SWXMLHash-3.0.2/Source/SWXMLHash+TypeConversion.swift","/SourceKitten/Packages/SWXMLHash-3.0.2/Source/SWXMLHash.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<Yaml.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "Yaml"
    module-output-path: "/SourceKitten/.build/debug/Yaml.swiftmodule"
    inputs: []
    outputs: ["<Yaml.module>","/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/Yaml.build"
    objects: ["/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Packages/Yaml-2.0.1/Yaml/Operators.swift","/SourceKitten/Packages/Yaml-2.0.1/Yaml/Parser.swift","/SourceKitten/Packages/Yaml-2.0.1/Yaml/Regex.swift","/SourceKitten/Packages/Yaml-2.0.1/Yaml/Result.swift","/SourceKitten/Packages/Yaml-2.0.1/Yaml/Tokenizer.swift","/SourceKitten/Packages/Yaml-2.0.1/Yaml/Yaml.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<sourcekitten.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "sourcekitten"
    module-output-path: "/SourceKitten/.build/debug/sourcekitten.swiftmodule"
    inputs: ["<Result.module>","<Nimble.module>","<Quick.module>","<Commandant.module>","<SWXMLHash.module>","<Yaml.module>","<Clang_C.module>","<SourceKit.module>","<SourceKittenFramework.module>"]
    outputs: ["<sourcekitten.module>","/SourceKitten/.build/debug/sourcekitten.build/CompleteCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/DocCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/Errors.swift.o","/SourceKitten/.build/debug/sourcekitten.build/FormatCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/IndexCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/main.swift.o","/SourceKitten/.build/debug/sourcekitten.build/StructureCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/SyntaxCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/VersionCommand.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/sourcekitten.build"
    objects: ["/SourceKitten/.build/debug/sourcekitten.build/CompleteCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/DocCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/Errors.swift.o","/SourceKitten/.build/debug/sourcekitten.build/FormatCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/IndexCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/main.swift.o","/SourceKitten/.build/debug/sourcekitten.build/StructureCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/SyntaxCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/VersionCommand.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-Xcc","-fmodule-map-file=/SourceKitten/Packages/Clang_C-1.0.1/module.modulemap","-Xcc","-fmodule-map-file=/SourceKitten/Packages/SourceKit-1.0.1/module.modulemap","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Source/sourcekitten/CompleteCommand.swift","/SourceKitten/Source/sourcekitten/DocCommand.swift","/SourceKitten/Source/sourcekitten/Errors.swift","/SourceKitten/Source/sourcekitten/FormatCommand.swift","/SourceKitten/Source/sourcekitten/IndexCommand.swift","/SourceKitten/Source/sourcekitten/main.swift","/SourceKitten/Source/sourcekitten/StructureCommand.swift","/SourceKitten/Source/sourcekitten/SyntaxCommand.swift","/SourceKitten/Source/sourcekitten/VersionCommand.swift"]
    is-library: false
    enable-whole-module-optimization: false
    num-threads: "8"

  "<SourceKittenFramework.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "SourceKittenFramework"
    module-output-path: "/SourceKitten/.build/debug/SourceKittenFramework.swiftmodule"
    inputs: ["<Result.module>","<Nimble.module>","<Quick.module>","<Commandant.module>","<SWXMLHash.module>","<Yaml.module>","<Clang_C.module>","<SourceKit.module>"]
    outputs: ["<SourceKittenFramework.module>","/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/SourceKittenFramework.build"
    objects: ["/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-Xcc","-fmodule-map-file=/SourceKitten/Packages/Clang_C-1.0.1/module.modulemap","-Xcc","-fmodule-map-file=/SourceKitten/Packages/SourceKit-1.0.1/module.modulemap","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Source/SourceKittenFramework/Clang+SourceKitten.swift","/SourceKitten/Source/SourceKittenFramework/ClangTranslationUnit.swift","/SourceKitten/Source/SourceKittenFramework/CodeCompletionItem.swift","/SourceKitten/Source/SourceKittenFramework/Dictionary+Merge.swift","/SourceKitten/Source/SourceKittenFramework/Documentation.swift","/SourceKitten/Source/SourceKittenFramework/File.swift","/SourceKitten/Source/SourceKittenFramework/JSONOutput.swift","/SourceKitten/Source/SourceKittenFramework/Language.swift","/SourceKitten/Source/SourceKittenFramework/library_wrapper.swift","/SourceKitten/Source/SourceKittenFramework/library_wrapper_CXString.swift","/SourceKitten/Source/SourceKittenFramework/library_wrapper_Documentation.swift","/SourceKitten/Source/SourceKittenFramework/library_wrapper_Index.swift","/SourceKitten/Source/SourceKittenFramework/library_wrapper_sourcekitd.swift","/SourceKitten/Source/SourceKittenFramework/LinuxCompatibility.swift","/SourceKitten/Source/SourceKittenFramework/Module.swift","/SourceKitten/Source/SourceKittenFramework/ObjCDeclarationKind.swift","/SourceKitten/Source/SourceKittenFramework/OffsetMap.swift","/SourceKitten/Source/SourceKittenFramework/Parameter.swift","/SourceKitten/Source/SourceKittenFramework/Request.swift","/SourceKitten/Source/SourceKittenFramework/SourceDeclaration.swift","/SourceKitten/Source/SourceKittenFramework/SourceLocation.swift","/SourceKitten/Source/SourceKittenFramework/StatementKind.swift","/SourceKitten/Source/SourceKittenFramework/String+SourceKitten.swift","/SourceKitten/Source/SourceKittenFramework/Structure.swift","/SourceKitten/Source/SourceKittenFramework/SwiftDeclarationKind.swift","/SourceKitten/Source/SourceKittenFramework/SwiftDocKey.swift","/SourceKitten/Source/SourceKittenFramework/SwiftDocs.swift","/SourceKitten/Source/SourceKittenFramework/SwiftLangSyntax.swift","/SourceKitten/Source/SourceKittenFramework/SyntaxKind.swift","/SourceKitten/Source/SourceKittenFramework/SyntaxMap.swift","/SourceKitten/Source/SourceKittenFramework/SyntaxToken.swift","/SourceKitten/Source/SourceKittenFramework/Text.swift","/SourceKitten/Source/SourceKittenFramework/Xcode.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<SourceKittenFrameworkTests.module>":
    tool: swift-compiler
    executable: "/usr/bin/swiftc"
    module-name: "SourceKittenFrameworkTests"
    module-output-path: "/SourceKitten/.build/debug/SourceKittenFrameworkTests.swiftmodule"
    inputs: ["<Result.module>","<Nimble.module>","<Quick.module>","<Commandant.module>","<SWXMLHash.module>","<Yaml.module>","<Clang_C.module>","<SourceKit.module>","<SourceKittenFramework.module>"]
    outputs: ["<SourceKittenFrameworkTests.module>","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ClangTranslationUnitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/CodeCompletionTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/DocInfoTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/FileTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ModuleTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/OffsetMapTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SourceKitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StringTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StructureTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SwiftDocsTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SyntaxTests.swift.o"]
    import-paths: ["/SourceKitten/.build/debug"]
    temps-path: "/SourceKitten/.build/debug/SourceKittenFrameworkTests.build"
    objects: ["/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ClangTranslationUnitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/CodeCompletionTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/DocInfoTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/FileTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ModuleTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/OffsetMapTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SourceKitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StringTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StructureTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SwiftDocsTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SyntaxTests.swift.o"]
    other-args: ["-j8","-D","SWIFT_PACKAGE","-Onone","-g","-enable-testing","-Xcc","-fmodule-map-file=/SourceKitten/Packages/Clang_C-1.0.1/module.modulemap","-Xcc","-fmodule-map-file=/SourceKitten/Packages/SourceKit-1.0.1/module.modulemap","-module-cache-path","/SourceKitten/.build/debug/ModuleCache"]
    sources: ["/SourceKitten/Tests/SourceKittenFrameworkTests/ClangTranslationUnitTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/CodeCompletionTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/DocInfoTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/FileTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/ModuleTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/OffsetMapTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/SourceKitTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/StringTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/StructureTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/SwiftDocsTests.swift","/SourceKitten/Tests/SourceKittenFrameworkTests/SyntaxTests.swift"]
    is-library: true
    enable-whole-module-optimization: false
    num-threads: "8"

  "<sourcekitten.exe>":
    tool: shell
    description: "Linking ./.build/debug/sourcekitten"
    inputs: ["/SourceKitten/.build/debug/sourcekitten.build/CompleteCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/DocCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/Errors.swift.o","/SourceKitten/.build/debug/sourcekitten.build/FormatCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/IndexCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/main.swift.o","/SourceKitten/.build/debug/sourcekitten.build/StructureCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/SyntaxCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/VersionCommand.swift.o","/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o","/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o","/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o","/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]
    outputs: ["<sourcekitten.exe>","/SourceKitten/.build/debug/sourcekitten"]
    args: ["/usr/bin/swiftc","-Xlinker","-rpath=$ORIGIN","-g","-L/SourceKitten/.build/debug","-o","/SourceKitten/.build/debug/sourcekitten","-emit-executable","/SourceKitten/.build/debug/sourcekitten.build/CompleteCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/DocCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/Errors.swift.o","/SourceKitten/.build/debug/sourcekitten.build/FormatCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/IndexCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/main.swift.o","/SourceKitten/.build/debug/sourcekitten.build/StructureCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/SyntaxCommand.swift.o","/SourceKitten/.build/debug/sourcekitten.build/VersionCommand.swift.o","/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o","/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o","/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o","/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]

  "<SourceKittenPackageTests.test>":
    tool: shell
    description: "Linking ./.build/debug/SourceKittenPackageTests.xctest"
    inputs: ["/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ClangTranslationUnitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/CodeCompletionTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/DocInfoTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/FileTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ModuleTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/OffsetMapTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SourceKitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StringTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StructureTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SwiftDocsTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SyntaxTests.swift.o","/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o","/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o","/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o","/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]
    outputs: ["<SourceKittenPackageTests.test>","/SourceKitten/.build/debug/SourceKittenPackageTests.xctest"]
    args: ["/usr/bin/swiftc","-Xlinker","-rpath=$ORIGIN","-g","-L/SourceKitten/.build/debug","-o","/SourceKitten/.build/debug/SourceKittenPackageTests.xctest","-module-name","SourceKittenPackageTests","/SourceKitten/Tests/LinuxMain.swift","-Xcc","-fmodule-map-file=/SourceKitten/Packages/Clang_C-1.0.1/module.modulemap","-Xcc","-fmodule-map-file=/SourceKitten/Packages/SourceKit-1.0.1/module.modulemap","-emit-executable","-I","/SourceKitten/.build/debug","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ClangTranslationUnitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/CodeCompletionTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/DocInfoTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/FileTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/ModuleTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/OffsetMapTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SourceKitTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StringTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/StructureTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SwiftDocsTests.swift.o","/SourceKitten/.build/debug/SourceKittenFrameworkTests.build/SyntaxTests.swift.o","/SourceKitten/.build/debug/Result.build/Result.swift.o","/SourceKitten/.build/debug/Result.build/ResultProtocol.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL+Wait.swift.o","/SourceKitten/.build/debug/Nimble.build/DSL.swift.o","/SourceKitten/.build/debug/Nimble.build/Expectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Expression.swift.o","/SourceKitten/.build/debug/Nimble.build/FailureMessage.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AdapterProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionDispatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/AssertionRecorder.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleEnvironment.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NimbleXCTestHandler.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBExpectation.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NMBObjCMatcher.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AllPass.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/AsyncMatcherWrapper.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAKindOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeAnInstanceOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeCloseTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeEmpty.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeginWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeGreaterThanOrEqualTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeIdenticalTo.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThan.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLessThanOrEqual.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeLogical.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeNil.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/BeVoid.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Contain.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/EndWith.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Equal.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/HaveCount.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/Match.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherFunc.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatcherProtocols.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/MatchError.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/PostNotification.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/RaisesException.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/SatisfyAnyOf.swift.o","/SourceKitten/.build/debug/Nimble.build/Matchers/ThrowError.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Async.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Errors.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Functional.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/SourceLocation.swift.o","/SourceKitten/.build/debug/Nimble.build/Utils/Stringers.swift.o","/SourceKitten/.build/debug/Nimble.build/Adapters/NonObjectiveC/ExceptionCapture.swift.o","/SourceKitten/.build/debug/Quick.build/Callsite.swift.o","/SourceKitten/.build/debug/Quick.build/ErrorUtility.swift.o","/SourceKitten/.build/debug/Quick.build/Example.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleGroup.swift.o","/SourceKitten/.build/debug/Quick.build/ExampleMetadata.swift.o","/SourceKitten/.build/debug/Quick.build/Filter.swift.o","/SourceKitten/.build/debug/Quick.build/NSBundle+CurrentTestBundle.swift.o","/SourceKitten/.build/debug/Quick.build/QuickMain.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSelectedTestSuiteBuilder.swift.o","/SourceKitten/.build/debug/Quick.build/QuickSpec.swift.o","/SourceKitten/.build/debug/Quick.build/QuickTestSuite.swift.o","/SourceKitten/.build/debug/Quick.build/String+FileName.swift.o","/SourceKitten/.build/debug/Quick.build/World.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/Configuration.swift.o","/SourceKitten/.build/debug/Quick.build/Configuration/QuickConfiguration.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/DSL.swift.o","/SourceKitten/.build/debug/Quick.build/DSL/World+DSL.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/Closures.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/ExampleHooks.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/HooksPhase.swift.o","/SourceKitten/.build/debug/Quick.build/Hooks/SuiteHooks.swift.o","/SourceKitten/.build/debug/Commandant.build/Argument.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentParser.swift.o","/SourceKitten/.build/debug/Commandant.build/ArgumentProtocol.swift.o","/SourceKitten/.build/debug/Commandant.build/Command.swift.o","/SourceKitten/.build/debug/Commandant.build/Errors.swift.o","/SourceKitten/.build/debug/Commandant.build/HelpCommand.swift.o","/SourceKitten/.build/debug/Commandant.build/LinuxSupport.swift.o","/SourceKitten/.build/debug/Commandant.build/Option.swift.o","/SourceKitten/.build/debug/Commandant.build/Switch.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash+TypeConversion.swift.o","/SourceKitten/.build/debug/SWXMLHash.build/SWXMLHash.swift.o","/SourceKitten/.build/debug/Yaml.build/Operators.swift.o","/SourceKitten/.build/debug/Yaml.build/Parser.swift.o","/SourceKitten/.build/debug/Yaml.build/Regex.swift.o","/SourceKitten/.build/debug/Yaml.build/Result.swift.o","/SourceKitten/.build/debug/Yaml.build/Tokenizer.swift.o","/SourceKitten/.build/debug/Yaml.build/Yaml.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Clang+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ClangTranslationUnit.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/CodeCompletionItem.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Dictionary+Merge.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/File.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/JSONOutput.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Language.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_CXString.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Documentation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_Index.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/library_wrapper_sourcekitd.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/LinuxCompatibility.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Module.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/ObjCDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/OffsetMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Parameter.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Request.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceDeclaration.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SourceLocation.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/StatementKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/String+SourceKitten.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Structure.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDeclarationKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocKey.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftDocs.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SwiftLangSyntax.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxKind.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxMap.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/SyntaxToken.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Text.swift.o","/SourceKitten/.build/debug/SourceKittenFramework.build/Xcode.swift.o"]

