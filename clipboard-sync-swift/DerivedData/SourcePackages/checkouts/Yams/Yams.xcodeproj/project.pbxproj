// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		6C0409A81E602E9A00C95D83 /* Node.Mapping.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0409A71E602E9A00C95D83 /* Node.Mapping.swift */; };
		6C0409AA1E6033DF00C95D83 /* Node.Sequence.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0409A91E6033DF00C95D83 /* Node.Sequence.swift */; };
		6C0409AC1E607E9900C95D83 /* Node.Scalar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0409AB1E607E9900C95D83 /* Node.Scalar.swift */; };
		6C0488EC1E0BE113006F9F80 /* TestHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0488EB1E0BE113006F9F80 /* TestHelper.swift */; };
		6C0488EE1E0CBD56006F9F80 /* ConstructorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0488ED1E0CBD56006F9F80 /* ConstructorTests.swift */; };
		6C0A00D51E152D6200222704 /* EmitterTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0A00D41E152D6200222704 /* EmitterTests.swift */; };
		6C0D2A361E0A934B00C45545 /* Constructor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0D2A351E0A934B00C45545 /* Constructor.swift */; };
		6C3C90B91E0FFB6B009DEFE8 /* NodeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C3C90B81E0FFB6B009DEFE8 /* NodeTests.swift */; };
		6C4A22071DF8553C002A0206 /* String+Yams.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C4A22061DF8553C002A0206 /* String+Yams.swift */; };
		6C4A22091DF855BB002A0206 /* StringTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C4A22081DF855BB002A0206 /* StringTests.swift */; };
		6C4AF3201EBE1705008775BC /* Decoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C4AF31E1EBE14A1008775BC /* Decoder.swift */; };
		6C6834C81E0281880047B4D1 /* Node.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834C71E0281880047B4D1 /* Node.swift */; };
		6C6834CC1E0283980047B4D1 /* Parser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834CB1E0283980047B4D1 /* Parser.swift */; };
		6C6834CD1E02847D0047B4D1 /* Tag.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834C91E0281D90047B4D1 /* Tag.swift */; };
		6C6834D11E0297390047B4D1 /* SpecTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834D01E0297390047B4D1 /* SpecTests.swift */; };
		6C6834D31E02B9760047B4D1 /* Resolver.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834D21E02B9760047B4D1 /* Resolver.swift */; };
		6C6834D51E02BC1F0047B4D1 /* ResolverTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6834D41E02BC1F0047B4D1 /* ResolverTests.swift */; };
		6C788A011EB87232005386F0 /* EncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C788A001EB87232005386F0 /* EncoderTests.swift */; };
		6C788A041EB89162005386F0 /* Encoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C788A021EB876C4005386F0 /* Encoder.swift */; };
		6C78C5651E29B27D0096215F /* RepresenterTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C78C5631E29B1CE0096215F /* RepresenterTests.swift */; };
		6CBAEE1A1E3839500021BF87 /* Yams.h in Headers */ = {isa = PBXBuildFile; fileRef = 6CBAEE191E3839500021BF87 /* Yams.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6CC2E33F1E22347B00F62269 /* Representer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CC2E33E1E22347B00F62269 /* Representer.swift */; };
		6CE603981E13502E00A13D8D /* Emitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CE603971E13502E00A13D8D /* Emitter.swift */; };
		6CF025381E9CF4380061FB47 /* Mark.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CF025371E9CF4380061FB47 /* Mark.swift */; };
		6CF0253A1E9D12680061FB47 /* MarkTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CF025391E9D12680061FB47 /* MarkTests.swift */; };
		6CF6CE091E0E3B1000CB87D4 /* PerformanceTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CF6CE081E0E3B1000CB87D4 /* PerformanceTests.swift */; };
		8FA807DC24B250EF0082215D /* TopLevelDecoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FA807DB24B250EF0082215D /* TopLevelDecoderTests.swift */; };
		8FBD7F7F2CB70C8900271BB9 /* Node.Alias.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F7B2CB70C8900271BB9 /* Node.Alias.swift */; };
		8FBD7F802CB70C8900271BB9 /* YamlAnchorProviding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F7D2CB70C8900271BB9 /* YamlAnchorProviding.swift */; };
		8FBD7F812CB70C8900271BB9 /* YamlTagProviding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F7E2CB70C8900271BB9 /* YamlTagProviding.swift */; };
		8FBD7F822CB70C8900271BB9 /* RedundancyAliasingStrategy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F7C2CB70C8900271BB9 /* RedundancyAliasingStrategy.swift */; };
		8FBD7F832CB70C8900271BB9 /* Anchor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F7A2CB70C8900271BB9 /* Anchor.swift */; };
		8FBD7F892CB70CFB00271BB9 /* NodeDecoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F862CB70CFB00271BB9 /* NodeDecoderTests.swift */; };
		8FBD7F8A2CB70CFB00271BB9 /* AnchorCodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F842CB70CFB00271BB9 /* AnchorCodingTests.swift */; };
		8FBD7F8B2CB70CFB00271BB9 /* AnchorTolerancesTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F852CB70CFB00271BB9 /* AnchorTolerancesTests.swift */; };
		8FBD7F8C2CB70CFB00271BB9 /* TagTolerancesTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F882CB70CFB00271BB9 /* TagTolerancesTests.swift */; };
		8FBD7F8D2CB70CFB00271BB9 /* TagCodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FBD7F872CB70CFB00271BB9 /* TagCodingTests.swift */; };
		8FCB2E202CF6D82700550869 /* ClassReferenceDecodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FCB2E1F2CF6D82700550869 /* ClassReferenceDecodingTests.swift */; };
		8FCB2E222CF6D83F00550869 /* AliasDereferencingStrategy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FCB2E212CF6D83F00550869 /* AliasDereferencingStrategy.swift */; };
		E8EDB8851DE2181B0062268D /* api.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_10 /* api.c */; };
		E8EDB8871DE2181B0062268D /* emitter.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_12 /* emitter.c */; };
		E8EDB8891DE2181B0062268D /* parser.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_14 /* parser.c */; };
		E8EDB88A1DE2181B0062268D /* reader.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_15 /* reader.c */; };
		E8EDB88B1DE2181B0062268D /* scanner.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_16 /* scanner.c */; };
		E8EDB88C1DE2181B0062268D /* writer.c in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_17 /* writer.c */; };
		E8EDB88E1DE218420062268D /* yaml.h in Headers */ = {isa = PBXBuildFile; fileRef = OBJ_19 /* yaml.h */; settings = {ATTRIBUTES = (Public, ); }; };
		OBJ_50 /* YamlError.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_22 /* YamlError.swift */; };
		OBJ_59 /* YamlErrorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_25 /* YamlErrorTests.swift */; };
		OBJ_62 /* Yams.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = OBJ_28 /* Yams.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E8C81DAA1DE0FE6600D40FD7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = OBJ_1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = OBJ_45;
			remoteInfo = Yams;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		6C0409A71E602E9A00C95D83 /* Node.Mapping.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Node.Mapping.swift; sourceTree = "<group>"; };
		6C0409A91E6033DF00C95D83 /* Node.Sequence.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Node.Sequence.swift; sourceTree = "<group>"; };
		6C0409AB1E607E9900C95D83 /* Node.Scalar.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Node.Scalar.swift; sourceTree = "<group>"; };
		6C0488EB1E0BE113006F9F80 /* TestHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TestHelper.swift; sourceTree = "<group>"; };
		6C0488ED1E0CBD56006F9F80 /* ConstructorTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConstructorTests.swift; sourceTree = "<group>"; };
		6C0A00D41E152D6200222704 /* EmitterTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EmitterTests.swift; sourceTree = "<group>"; };
		6C0D2A351E0A934B00C45545 /* Constructor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constructor.swift; sourceTree = "<group>"; };
		6C3C90B81E0FFB6B009DEFE8 /* NodeTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NodeTests.swift; sourceTree = "<group>"; };
		6C4A22061DF8553C002A0206 /* String+Yams.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+Yams.swift"; sourceTree = "<group>"; };
		6C4A22081DF855BB002A0206 /* StringTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StringTests.swift; sourceTree = "<group>"; };
		6C4A220A1DF85793002A0206 /* LinuxMain.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LinuxMain.swift; sourceTree = "<group>"; };
		6C4AF31E1EBE14A1008775BC /* Decoder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Decoder.swift; sourceTree = "<group>"; };
		6C6834C71E0281880047B4D1 /* Node.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Node.swift; sourceTree = "<group>"; };
		6C6834C91E0281D90047B4D1 /* Tag.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Tag.swift; sourceTree = "<group>"; };
		6C6834CB1E0283980047B4D1 /* Parser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Parser.swift; sourceTree = "<group>"; };
		6C6834D01E0297390047B4D1 /* SpecTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SpecTests.swift; sourceTree = "<group>"; };
		6C6834D21E02B9760047B4D1 /* Resolver.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Resolver.swift; sourceTree = "<group>"; };
		6C6834D41E02BC1F0047B4D1 /* ResolverTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ResolverTests.swift; sourceTree = "<group>"; };
		6C788A001EB87232005386F0 /* EncoderTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EncoderTests.swift; sourceTree = "<group>"; };
		6C788A021EB876C4005386F0 /* Encoder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Encoder.swift; sourceTree = "<group>"; };
		6C78C5631E29B1CE0096215F /* RepresenterTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RepresenterTests.swift; sourceTree = "<group>"; };
		6C9B503E22B6362B00D82F94 /* Yams.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; name = Yams.xctestplan; path = Yams.xcodeproj/Yams.xctestplan; sourceTree = "<group>"; };
		6CBAEE191E3839500021BF87 /* Yams.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Yams.h; sourceTree = "<group>"; };
		6CC2E33E1E22347B00F62269 /* Representer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Representer.swift; sourceTree = "<group>"; };
		6CE603971E13502E00A13D8D /* Emitter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Emitter.swift; sourceTree = "<group>"; };
		6CF025371E9CF4380061FB47 /* Mark.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Mark.swift; sourceTree = "<group>"; };
		6CF025391E9D12680061FB47 /* MarkTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarkTests.swift; sourceTree = "<group>"; };
		6CF6CE071E0E3A5900CB87D4 /* Fixtures */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Fixtures; sourceTree = "<group>"; };
		6CF6CE081E0E3B1000CB87D4 /* PerformanceTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PerformanceTests.swift; sourceTree = "<group>"; };
		8FA807DB24B250EF0082215D /* TopLevelDecoderTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopLevelDecoderTests.swift; sourceTree = "<group>"; };
		8FBD7F7A2CB70C8900271BB9 /* Anchor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Anchor.swift; sourceTree = "<group>"; };
		8FBD7F7B2CB70C8900271BB9 /* Node.Alias.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Node.Alias.swift; sourceTree = "<group>"; };
		8FBD7F7C2CB70C8900271BB9 /* RedundancyAliasingStrategy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RedundancyAliasingStrategy.swift; sourceTree = "<group>"; };
		8FBD7F7D2CB70C8900271BB9 /* YamlAnchorProviding.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YamlAnchorProviding.swift; sourceTree = "<group>"; };
		8FBD7F7E2CB70C8900271BB9 /* YamlTagProviding.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YamlTagProviding.swift; sourceTree = "<group>"; };
		8FBD7F842CB70CFB00271BB9 /* AnchorCodingTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnchorCodingTests.swift; sourceTree = "<group>"; };
		8FBD7F852CB70CFB00271BB9 /* AnchorTolerancesTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnchorTolerancesTests.swift; sourceTree = "<group>"; };
		8FBD7F862CB70CFB00271BB9 /* NodeDecoderTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NodeDecoderTests.swift; sourceTree = "<group>"; };
		8FBD7F872CB70CFB00271BB9 /* TagCodingTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagCodingTests.swift; sourceTree = "<group>"; };
		8FBD7F882CB70CFB00271BB9 /* TagTolerancesTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagTolerancesTests.swift; sourceTree = "<group>"; };
		8FCB2E1F2CF6D82700550869 /* ClassReferenceDecodingTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassReferenceDecodingTests.swift; sourceTree = "<group>"; };
		8FCB2E212CF6D83F00550869 /* AliasDereferencingStrategy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AliasDereferencingStrategy.swift; sourceTree = "<group>"; };
		OBJ_10 /* api.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = api.c; sourceTree = "<group>"; };
		OBJ_12 /* emitter.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = emitter.c; sourceTree = "<group>"; };
		OBJ_14 /* parser.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = parser.c; sourceTree = "<group>"; };
		OBJ_15 /* reader.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = reader.c; sourceTree = "<group>"; };
		OBJ_16 /* scanner.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = scanner.c; sourceTree = "<group>"; };
		OBJ_17 /* writer.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = writer.c; sourceTree = "<group>"; };
		OBJ_19 /* yaml.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = yaml.h; sourceTree = "<group>"; };
		OBJ_22 /* YamlError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YamlError.swift; sourceTree = "<group>"; };
		OBJ_25 /* YamlErrorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YamlErrorTests.swift; sourceTree = "<group>"; };
		OBJ_28 /* Yams.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Yams.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		OBJ_29 /* YamsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; path = YamsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		OBJ_6 /* Package.swift */ = {isa = PBXFileReference; explicitFileType = sourcecode.swift; path = Package.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		OBJ_51 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		OBJ_60 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 0;
			files = (
				OBJ_62 /* Yams.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		OBJ_18 /* include */ = {
			isa = PBXGroup;
			children = (
				OBJ_19 /* yaml.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		OBJ_21 /* Yams */ = {
			isa = PBXGroup;
			children = (
				8FCB2E212CF6D83F00550869 /* AliasDereferencingStrategy.swift */,
				8FBD7F7A2CB70C8900271BB9 /* Anchor.swift */,
				6C0D2A351E0A934B00C45545 /* Constructor.swift */,
				6C4AF31E1EBE14A1008775BC /* Decoder.swift */,
				6CE603971E13502E00A13D8D /* Emitter.swift */,
				6C788A021EB876C4005386F0 /* Encoder.swift */,
				6CF025371E9CF4380061FB47 /* Mark.swift */,
				6C6834C71E0281880047B4D1 /* Node.swift */,
				8FBD7F7B2CB70C8900271BB9 /* Node.Alias.swift */,
				6C0409A71E602E9A00C95D83 /* Node.Mapping.swift */,
				6C0409AB1E607E9900C95D83 /* Node.Scalar.swift */,
				6C0409A91E6033DF00C95D83 /* Node.Sequence.swift */,
				6C6834CB1E0283980047B4D1 /* Parser.swift */,
				8FBD7F7C2CB70C8900271BB9 /* RedundancyAliasingStrategy.swift */,
				6CC2E33E1E22347B00F62269 /* Representer.swift */,
				6C6834D21E02B9760047B4D1 /* Resolver.swift */,
				6C4A22061DF8553C002A0206 /* String+Yams.swift */,
				6C6834C91E0281D90047B4D1 /* Tag.swift */,
				8FBD7F7D2CB70C8900271BB9 /* YamlAnchorProviding.swift */,
				OBJ_22 /* YamlError.swift */,
				8FBD7F7E2CB70C8900271BB9 /* YamlTagProviding.swift */,
				6CBAEE191E3839500021BF87 /* Yams.h */,
			);
			name = Yams;
			path = Sources/Yams;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_23 /* Tests */ = {
			isa = PBXGroup;
			children = (
				6C4A220A1DF85793002A0206 /* LinuxMain.swift */,
				OBJ_24 /* YamsTests */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		OBJ_24 /* YamsTests */ = {
			isa = PBXGroup;
			children = (
				8FBD7F842CB70CFB00271BB9 /* AnchorCodingTests.swift */,
				8FBD7F852CB70CFB00271BB9 /* AnchorTolerancesTests.swift */,
				8FCB2E1F2CF6D82700550869 /* ClassReferenceDecodingTests.swift */,
				6C0488ED1E0CBD56006F9F80 /* ConstructorTests.swift */,
				6C0A00D41E152D6200222704 /* EmitterTests.swift */,
				6C788A001EB87232005386F0 /* EncoderTests.swift */,
				6CF6CE071E0E3A5900CB87D4 /* Fixtures */,
				6CF025391E9D12680061FB47 /* MarkTests.swift */,
				8FBD7F862CB70CFB00271BB9 /* NodeDecoderTests.swift */,
				6C3C90B81E0FFB6B009DEFE8 /* NodeTests.swift */,
				6CF6CE081E0E3B1000CB87D4 /* PerformanceTests.swift */,
				6C78C5631E29B1CE0096215F /* RepresenterTests.swift */,
				6C6834D41E02BC1F0047B4D1 /* ResolverTests.swift */,
				6C6834D01E0297390047B4D1 /* SpecTests.swift */,
				6C4A22081DF855BB002A0206 /* StringTests.swift */,
				8FBD7F872CB70CFB00271BB9 /* TagCodingTests.swift */,
				8FBD7F882CB70CFB00271BB9 /* TagTolerancesTests.swift */,
				6C0488EB1E0BE113006F9F80 /* TestHelper.swift */,
				8FA807DB24B250EF0082215D /* TopLevelDecoderTests.swift */,
				OBJ_25 /* YamlErrorTests.swift */,
			);
			name = YamsTests;
			path = Tests/YamsTests;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_26 /* Products */ = {
			isa = PBXGroup;
			children = (
				OBJ_28 /* Yams.framework */,
				OBJ_29 /* YamsTests.xctest */,
			);
			name = Products;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		OBJ_5 = {
			isa = PBXGroup;
			children = (
				6C9B503E22B6362B00D82F94 /* Yams.xctestplan */,
				OBJ_6 /* Package.swift */,
				OBJ_7 /* Sources */,
				OBJ_23 /* Tests */,
				OBJ_26 /* Products */,
			);
			sourceTree = "<group>";
		};
		OBJ_7 /* Sources */ = {
			isa = PBXGroup;
			children = (
				OBJ_8 /* CYaml */,
				OBJ_21 /* Yams */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		OBJ_8 /* CYaml */ = {
			isa = PBXGroup;
			children = (
				OBJ_9 /* src */,
				OBJ_18 /* include */,
			);
			name = CYaml;
			path = Sources/CYaml;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_9 /* src */ = {
			isa = PBXGroup;
			children = (
				OBJ_10 /* api.c */,
				OBJ_12 /* emitter.c */,
				OBJ_14 /* parser.c */,
				OBJ_15 /* reader.c */,
				OBJ_16 /* scanner.c */,
				OBJ_17 /* writer.c */,
			);
			path = src;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		E8EDB88D1DE2183E0062268D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8EDB88E1DE218420062268D /* yaml.h in Headers */,
				6CBAEE1A1E3839500021BF87 /* Yams.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		OBJ_45 /* Yams */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = OBJ_46 /* Build configuration list for PBXNativeTarget "Yams" */;
			buildPhases = (
				OBJ_49 /* Sources */,
				OBJ_51 /* Frameworks */,
				E8EDB88D1DE2183E0062268D /* Headers */,
				6C0D2A341E0A6F5000C45545 /* Run SwiftLint */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Yams;
			productName = Yams;
			productReference = OBJ_28 /* Yams.framework */;
			productType = "com.apple.product-type.framework";
		};
		OBJ_54 /* YamsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = OBJ_55 /* Build configuration list for PBXNativeTarget "YamsTests" */;
			buildPhases = (
				OBJ_58 /* Sources */,
				OBJ_60 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				OBJ_64 /* PBXTargetDependency */,
			);
			name = YamsTests;
			productName = YamsTests;
			productReference = OBJ_29 /* YamsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		OBJ_1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1220;
				TargetAttributes = {
					OBJ_45 = {
						LastSwiftMigration = 1100;
					};
					OBJ_54 = {
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = OBJ_2 /* Build configuration list for PBXProject "Yams" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = OBJ_5;
			productRefGroup = OBJ_26 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				OBJ_45 /* Yams */,
				OBJ_54 /* YamsTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		6C0D2A341E0A6F5000C45545 /* Run SwiftLint */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run SwiftLint";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if $CI eq true; then\n    # Don't run SwiftLint on CI — it's already running in a workflow\n    exit 0\nfi\n\nexport PATH=\"$PATH:/opt/homebrew/bin\"\nif which swiftlint > /dev/null; then\n  swiftlint\nelse\n  echo \"warning: SwiftLint not installed, download from https://github.com/realm/SwiftLint\"\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		OBJ_49 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 0;
			files = (
				E8EDB8871DE2181B0062268D /* emitter.c in Sources */,
				E8EDB8851DE2181B0062268D /* api.c in Sources */,
				6CC2E33F1E22347B00F62269 /* Representer.swift in Sources */,
				6CE603981E13502E00A13D8D /* Emitter.swift in Sources */,
				6C6834CC1E0283980047B4D1 /* Parser.swift in Sources */,
				6C6834C81E0281880047B4D1 /* Node.swift in Sources */,
				6C0D2A361E0A934B00C45545 /* Constructor.swift in Sources */,
				6C6834D31E02B9760047B4D1 /* Resolver.swift in Sources */,
				6C0409AA1E6033DF00C95D83 /* Node.Sequence.swift in Sources */,
				6C0409A81E602E9A00C95D83 /* Node.Mapping.swift in Sources */,
				E8EDB88C1DE2181B0062268D /* writer.c in Sources */,
				8FCB2E222CF6D83F00550869 /* AliasDereferencingStrategy.swift in Sources */,
				E8EDB88B1DE2181B0062268D /* scanner.c in Sources */,
				6C4AF3201EBE1705008775BC /* Decoder.swift in Sources */,
				6C0409AC1E607E9900C95D83 /* Node.Scalar.swift in Sources */,
				6C4A22071DF8553C002A0206 /* String+Yams.swift in Sources */,
				8FBD7F7F2CB70C8900271BB9 /* Node.Alias.swift in Sources */,
				8FBD7F802CB70C8900271BB9 /* YamlAnchorProviding.swift in Sources */,
				8FBD7F812CB70C8900271BB9 /* YamlTagProviding.swift in Sources */,
				8FBD7F822CB70C8900271BB9 /* RedundancyAliasingStrategy.swift in Sources */,
				8FBD7F832CB70C8900271BB9 /* Anchor.swift in Sources */,
				E8EDB8891DE2181B0062268D /* parser.c in Sources */,
				6CF025381E9CF4380061FB47 /* Mark.swift in Sources */,
				OBJ_50 /* YamlError.swift in Sources */,
				E8EDB88A1DE2181B0062268D /* reader.c in Sources */,
				6C788A041EB89162005386F0 /* Encoder.swift in Sources */,
				6C6834CD1E02847D0047B4D1 /* Tag.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		OBJ_58 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 0;
			files = (
				6C788A011EB87232005386F0 /* EncoderTests.swift in Sources */,
				6CF6CE091E0E3B1000CB87D4 /* PerformanceTests.swift in Sources */,
				6C4A22091DF855BB002A0206 /* StringTests.swift in Sources */,
				6C0488EC1E0BE113006F9F80 /* TestHelper.swift in Sources */,
				8FBD7F892CB70CFB00271BB9 /* NodeDecoderTests.swift in Sources */,
				8FBD7F8A2CB70CFB00271BB9 /* AnchorCodingTests.swift in Sources */,
				8FBD7F8B2CB70CFB00271BB9 /* AnchorTolerancesTests.swift in Sources */,
				8FCB2E202CF6D82700550869 /* ClassReferenceDecodingTests.swift in Sources */,
				8FBD7F8C2CB70CFB00271BB9 /* TagTolerancesTests.swift in Sources */,
				8FBD7F8D2CB70CFB00271BB9 /* TagCodingTests.swift in Sources */,
				6C78C5651E29B27D0096215F /* RepresenterTests.swift in Sources */,
				6C3C90B91E0FFB6B009DEFE8 /* NodeTests.swift in Sources */,
				6C0A00D51E152D6200222704 /* EmitterTests.swift in Sources */,
				8FA807DC24B250EF0082215D /* TopLevelDecoderTests.swift in Sources */,
				6CF0253A1E9D12680061FB47 /* MarkTests.swift in Sources */,
				6C0488EE1E0CBD56006F9F80 /* ConstructorTests.swift in Sources */,
				OBJ_59 /* YamlErrorTests.swift in Sources */,
				6C6834D11E0297390047B4D1 /* SpecTests.swift in Sources */,
				6C6834D51E02BC1F0047B4D1 /* ResolverTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		OBJ_64 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = OBJ_45 /* Yams */;
			targetProxy = E8C81DAA1DE0FE6600D40FD7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		OBJ_3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_SWIFT_FLAGS = "-DXcode";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "macosx iphoneos iphonesimulator appletvos appletvsimulator watchos watchsimulator";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Off;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 11.0;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Debug;
		};
		OBJ_4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				OTHER_SWIFT_FLAGS = "-DXcode";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "macosx iphoneos iphonesimulator appletvos appletvsimulator watchos watchsimulator";
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_SWIFT3_OBJC_INFERENCE = Off;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 11.0;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Release;
		};
		OBJ_47 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_TESTABILITY = YES;
				INFOPLIST_FILE = Yams.xcodeproj/Yams_Info.plist;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				PRODUCT_BUNDLE_IDENTIFIER = com.jpsim.Yams;
				PRODUCT_MODULE_NAME = "$(TARGET_NAME:c99extidentifier)";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGET_NAME = Yams;
				WARNING_CFLAGS = "-Wall";
			};
			name = Debug;
		};
		OBJ_48 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_TESTABILITY = YES;
				INFOPLIST_FILE = Yams.xcodeproj/Yams_Info.plist;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				PRODUCT_BUNDLE_IDENTIFIER = com.jpsim.Yams;
				PRODUCT_MODULE_NAME = "$(TARGET_NAME:c99extidentifier)";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGET_NAME = Yams;
				WARNING_CFLAGS = "-Wall";
			};
			name = Release;
		};
		OBJ_56 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				INFOPLIST_FILE = Yams.xcodeproj/YamsTests_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"@loader_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				TARGET_NAME = YamsTests;
			};
			name = Debug;
		};
		OBJ_57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				INFOPLIST_FILE = Yams.xcodeproj/YamsTests_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"@loader_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				TARGET_NAME = YamsTests;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		OBJ_2 /* Build configuration list for PBXProject "Yams" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_3 /* Debug */,
				OBJ_4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		OBJ_46 /* Build configuration list for PBXNativeTarget "Yams" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_47 /* Debug */,
				OBJ_48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		OBJ_55 /* Build configuration list for PBXNativeTarget "YamsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_56 /* Debug */,
				OBJ_57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = OBJ_1 /* Project object */;
}
