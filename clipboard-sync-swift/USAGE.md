# Clipboard Sync Swift版本使用指南

## 快速开始

### 1. 构建应用

```bash
cd clipboard-sync-swift
./build.sh
```

### 2. 启动应用

```bash
open ./ClipboardSync.app
```

或者双击 `ClipboardSync.app` 文件。

### 3. 配置同步

应用启动后会自动尝试导入现有的配置文件。如果需要手动配置：

1. 点击菜单栏的剪贴板图标
2. 选择 "Preferences..."
3. 在 Configuration 标签中设置你的 ntfy 主题

### 4. 开始同步

1. 在 Status 标签中点击 "Start Sync"
2. 或者通过菜单栏图标选择 "Start Sync"

## 详细配置

### 发送器设置

- **Enable Sender**: 是否启用发送功能
- **Ntfy Topic URL**: 发送目标的完整URL，例如 `https://ntfy.sh/your_topic_here`
- **Poll Interval**: 检查剪贴板变化的频率（秒）
- **Request Timeout**: HTTP请求超时时间（秒）
- **Filename Prefix**: 发送文件的前缀名

### 接收器设置

- **Enable Receiver**: 是否启用接收功能
- **Ntfy Server**: ntfy服务器地址，例如 `ntfy.sh`
- **Ntfy Topic**: 监听的主题名称
- **Reconnect Delay**: WebSocket重连延迟（秒）
- **Request Timeout**: 下载附件的超时时间（秒）

### 高级设置

- **Log Level**: 日志级别（DEBUG, INFO, WARNING, ERROR）
- **Enable macOS Image Support**: 是否支持图片剪贴板同步

## 与Python版本互通

Swift版本与Python版本完全兼容，可以：

1. **共享配置**: 自动导入现有的 `config/config.yaml`
2. **跨设备同步**: 一台设备运行Swift版本，另一台运行Python版本
3. **无缝切换**: 随时在两个版本之间切换

### 配置导入

Swift版本会按以下顺序查找配置文件：

1. 项目根目录的 `config/config.yaml`
2. 应用程序包内的 `config.yaml`
3. 用户主目录的 `~/.clipboard-sync/config.yaml`

找到配置文件后会自动转换格式并保存到 UserDefaults。

## 使用技巧

### 1. 菜单栏控制

- **左键点击**: 显示菜单
- **图标状态**: 
  - 空心图标 = 已停止
  - 实心图标 = 正在运行

### 2. 状态监控

在 Status 标签中可以查看：
- 当前运行状态
- WebSocket连接状态
- 发送/接收统计
- 最后活动时间

### 3. 日志查看

在 Logs 标签中可以：
- 查看详细的运行日志
- 开启/关闭自动滚动
- 清空日志记录

### 4. 快捷操作

- **Cmd+,**: 打开偏好设置（当主窗口激活时）
- **菜单栏右键**: 快速访问常用功能

## 故障排除

### 应用无法启动

1. 检查macOS版本（需要13.0+）
2. 确认应用签名和权限
3. 查看控制台日志

### 同步不工作

1. 检查网络连接
2. 验证ntfy主题配置
3. 查看应用日志
4. 确认防火墙设置

### 图片同步问题

1. 确认已启用图片支持
2. 检查图片格式（支持PNG、JPEG等）
3. 验证文件大小限制

### 配置导入失败

1. 检查YAML文件格式
2. 确认文件路径正确
3. 手动在界面中配置

## 性能优化

### 减少资源占用

1. 适当增加轮询间隔
2. 降低日志级别
3. 关闭不需要的功能

### 网络优化

1. 使用本地ntfy服务器
2. 调整超时设置
3. 优化重连间隔

## 安全考虑

### 数据隐私

- 剪贴板内容通过ntfy传输
- 建议使用私有ntfy服务器
- 考虑启用ntfy的访问控制

### 网络安全

- 默认使用HTTPS/WSS加密传输
- 避免在公共网络上使用
- 定期更换ntfy主题

## 开发和调试

### 启用调试模式

1. 将日志级别设置为 DEBUG
2. 查看详细的网络请求日志
3. 监控剪贴板变化事件

### 自定义配置

可以通过修改 `ConfigManager.swift` 来：
- 添加新的配置选项
- 自定义默认值
- 实现配置验证

### 扩展功能

Swift版本的模块化设计便于扩展：
- 添加新的剪贴板类型支持
- 实现自定义网络协议
- 集成其他同步服务

## 更新和维护

### 检查更新

目前需要手动检查项目更新，未来版本将添加自动更新功能。

### 备份配置

配置保存在 UserDefaults 中，可以通过以下方式备份：

```bash
defaults export com.clipboardsync.app clipboard-sync-config.plist
```

恢复配置：

```bash
defaults import com.clipboardsync.app clipboard-sync-config.plist
```
