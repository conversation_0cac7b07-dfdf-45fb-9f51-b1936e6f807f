#!/bin/bash

# Clipboard Sync Swift版本构建脚本

set -e

echo "🚀 开始构建 Clipboard Sync Swift版本..."

# 检查Xcode是否可用
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: 需要安装Xcode或Xcode Command Line Tools"
    echo "   请从App Store安装Xcode，或运行: xcode-select --install"
    exit 1
fi

# 检查项目文件
if [ ! -f "ClipboardSync.xcodeproj/project.pbxproj" ]; then
    echo "❌ 错误: 找不到Xcode项目文件"
    exit 1
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf build/
rm -rf DerivedData/

# 构建项目
echo "🔨 构建项目..."
xcodebuild \
    -project ClipboardSync.xcodeproj \
    -scheme ClipboardSync \
    -configuration Release \
    -derivedDataPath ./DerivedData \
    build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功!"
    
    # 查找构建的应用
    APP_PATH=$(find ./DerivedData -name "ClipboardSync.app" -type d | head -1)
    
    if [ -n "$APP_PATH" ]; then
        echo "📱 应用位置: $APP_PATH"
        echo "🎉 可以运行以下命令启动应用:"
        echo "   open \"$APP_PATH\""
        
        # 创建符号链接到方便的位置
        ln -sf "$APP_PATH" "./ClipboardSync.app"
        echo "📎 已创建符号链接: ./ClipboardSync.app"
    else
        echo "⚠️  警告: 找不到构建的应用文件"
    fi
else
    echo "❌ 构建失败!"
    exit 1
fi

echo ""
echo "🎯 下一步:"
echo "   1. 运行应用: open ./ClipboardSync.app"
echo "   2. 配置ntfy主题和服务器"
echo "   3. 启动剪贴板同步"
echo ""
echo "📚 更多信息请查看 README.md"
