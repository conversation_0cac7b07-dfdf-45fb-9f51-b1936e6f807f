<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="#2C5F8A" stroke-width="8"/>
  
  <!-- Clipboard base -->
  <rect x="160" y="120" width="192" height="272" rx="16" ry="16" fill="#FFFFFF" stroke="#2C5F8A" stroke-width="4"/>
  
  <!-- Clipboard clip -->
  <rect x="200" y="80" width="112" height="80" rx="8" ry="8" fill="#E8E8E8" stroke="#2C5F8A" stroke-width="3"/>
  <rect x="220" y="100" width="72" height="40" rx="4" ry="4" fill="#FFFFFF" stroke="#2C5F8A" stroke-width="2"/>
  
  <!-- Document lines -->
  <line x1="180" y1="180" x2="332" y2="180" stroke="#4A90E2" stroke-width="3" stroke-linecap="round"/>
  <line x1="180" y1="210" x2="300" y2="210" stroke="#4A90E2" stroke-width="3" stroke-linecap="round"/>
  <line x1="180" y1="240" x2="320" y2="240" stroke="#4A90E2" stroke-width="3" stroke-linecap="round"/>
  <line x1="180" y1="270" x2="280" y2="270" stroke="#4A90E2" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Sync arrows -->
  <g transform="translate(256, 320)">
    <!-- Right arrow -->
    <path d="M -20 -10 L 0 -10 L 0 -20 L 20 0 L 0 20 L 0 10 L -20 10 Z" fill="#4A90E2"/>
    <!-- Left arrow -->
    <path d="M 20 -10 L 0 -10 L 0 -20 L -20 0 L 0 20 L 0 10 L 20 10 Z" fill="#4A90E2" transform="translate(0, 30)"/>
  </g>
</svg>
