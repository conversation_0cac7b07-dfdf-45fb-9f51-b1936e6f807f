{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./App.css';// Type definitions\n// Declare global electronAPI\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[config,setConfig]=useState(null);const[syncStatus,setSyncStatus]=useState({isRunning:false});const[logs,setLogs]=useState([]);const[activeTab,setActiveTab]=useState('config');const[loading,setLoading]=useState(false);// Load initial data\nuseEffect(()=>{const loadData=async()=>{try{const[configData,statusData]=await Promise.all([window.electronAPI.getConfig(),window.electronAPI.getSyncStatus()]);setConfig(configData);setSyncStatus(statusData);}catch(error){console.error('Failed to load initial data:',error);}};loadData();// Set up event listeners\nconst unsubscribeStatus=window.electronAPI.onSyncStatusChanged((event,data)=>{setSyncStatus(data);});const unsubscribeOutput=window.electronAPI.onPythonOutput((event,data)=>{const timestamp=new Date().toLocaleTimeString();const logEntry=\"[\".concat(timestamp,\"] \").concat(data.type.toUpperCase(),\": \").concat(data.data);setLogs(prev=>[...prev.slice(-99),logEntry]);// Keep last 100 logs\n});return()=>{unsubscribeStatus();unsubscribeOutput();};},[]);const handleStartSync=async()=>{if(!config)return;setLoading(true);try{const result=await window.electronAPI.startSync(config);if(!result.success){alert(\"Failed to start sync: \".concat(result.error));}}catch(error){alert(\"Error starting sync: \".concat(error));}finally{setLoading(false);}};const handleStopSync=async()=>{setLoading(true);try{const result=await window.electronAPI.stopSync();if(!result.success){alert(\"Failed to stop sync: \".concat(result.error));}}catch(error){alert(\"Error stopping sync: \".concat(error));}finally{setLoading(false);}};const handleSaveConfig=async()=>{if(!config)return;setLoading(true);try{const result=await window.electronAPI.saveConfig(config);if(result.success){alert('Configuration saved successfully!');}else{alert('Failed to save configuration');}}catch(error){alert(\"Error saving configuration: \".concat(error));}finally{setLoading(false);}};const updateConfig=(path,value)=>{if(!config)return;const keys=path.split('.');const newConfig=_objectSpread({},config);let current=newConfig;for(let i=0;i<keys.length-1;i++){current=current[keys[i]];}current[keys[keys.length-1]]=value;setConfig(newConfig);};if(!config){return/*#__PURE__*/_jsxs(\"div\",{className:\"app-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading configuration...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"app\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"app-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Clipboard Sync\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-indicator\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"status-dot \".concat(syncStatus.isRunning?'running':'stopped')}),/*#__PURE__*/_jsx(\"span\",{children:syncStatus.isRunning?'Running':'Stopped'}),syncStatus.pid&&/*#__PURE__*/_jsxs(\"span\",{className:\"pid\",children:[\"PID: \",syncStatus.pid]})]})]}),/*#__PURE__*/_jsxs(\"nav\",{className:\"app-nav\",children:[/*#__PURE__*/_jsx(\"button\",{className:activeTab==='config'?'active':'',onClick:()=>setActiveTab('config'),children:\"Configuration\"}),/*#__PURE__*/_jsx(\"button\",{className:activeTab==='status'?'active':'',onClick:()=>setActiveTab('status'),children:\"Status\"}),/*#__PURE__*/_jsx(\"button\",{className:activeTab==='logs'?'active':'',onClick:()=>setActiveTab('logs'),children:\"Logs\"})]}),/*#__PURE__*/_jsxs(\"main\",{className:\"app-main\",children:[activeTab==='config'&&/*#__PURE__*/_jsxs(\"div\",{className:\"config-panel\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Configuration\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Sender Settings\"}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:config.sender.enabled,onChange:e=>updateConfig('sender.enabled',e.target.checked)}),\"Enable Sender\"]}),/*#__PURE__*/_jsxs(\"label\",{children:[\"Ntfy Topic URL:\",/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.sender.ntfy_topic_url,onChange:e=>updateConfig('sender.ntfy_topic_url',e.target.value),placeholder:\"https://ntfy.sh/your_topic_here\"})]}),/*#__PURE__*/_jsxs(\"label\",{children:[\"Poll Interval (seconds):\",/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:config.sender.poll_interval_seconds,onChange:e=>updateConfig('sender.poll_interval_seconds',parseFloat(e.target.value)),min:\"0.1\",step:\"0.1\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Receiver Settings\"}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:config.receiver.enabled,onChange:e=>updateConfig('receiver.enabled',e.target.checked)}),\"Enable Receiver\"]}),/*#__PURE__*/_jsxs(\"label\",{children:[\"Ntfy Server:\",/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.receiver.ntfy_server,onChange:e=>updateConfig('receiver.ntfy_server',e.target.value),placeholder:\"ntfy.sh\"})]}),/*#__PURE__*/_jsxs(\"label\",{children:[\"Ntfy Topic:\",/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.receiver.ntfy_topic,onChange:e=>updateConfig('receiver.ntfy_topic',e.target.value),placeholder:\"your_receive_topic_here\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Advanced Settings\"}),/*#__PURE__*/_jsxs(\"label\",{children:[\"Log Level:\",/*#__PURE__*/_jsxs(\"select\",{value:config.logging.level,onChange:e=>updateConfig('logging.level',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"DEBUG\",children:\"Debug\"}),/*#__PURE__*/_jsx(\"option\",{value:\"INFO\",children:\"Info\"}),/*#__PURE__*/_jsx(\"option\",{value:\"WARNING\",children:\"Warning\"}),/*#__PURE__*/_jsx(\"option\",{value:\"ERROR\",children:\"Error\"})]})]}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:config.macos.image_support,onChange:e=>updateConfig('macos.image_support',e.target.checked)}),\"Enable macOS Image Support\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"config-actions\",children:/*#__PURE__*/_jsx(\"button\",{onClick:handleSaveConfig,disabled:loading,children:loading?'Saving...':'Save Configuration'})})]}),activeTab==='status'&&/*#__PURE__*/_jsxs(\"div\",{className:\"status-panel\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Sync Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Status:\"}),/*#__PURE__*/_jsx(\"span\",{className:syncStatus.isRunning?'status-running':'status-stopped',children:syncStatus.isRunning?'Running':'Stopped'})]}),syncStatus.pid&&/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Process ID:\"}),\" \",syncStatus.pid]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Sender:\"}),\" \",config.sender.enabled?'Enabled':'Disabled']}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Receiver:\"}),\" \",config.receiver.enabled?'Enabled':'Disabled']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleStartSync,disabled:loading||syncStatus.isRunning,className:\"start-button\",children:loading?'Starting...':'Start Sync'}),/*#__PURE__*/_jsx(\"button\",{onClick:handleStopSync,disabled:loading||!syncStatus.isRunning,className:\"stop-button\",children:loading?'Stopping...':'Stop Sync'})]})]}),activeTab==='logs'&&/*#__PURE__*/_jsxs(\"div\",{className:\"logs-panel\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Logs\"}),/*#__PURE__*/_jsx(\"div\",{className:\"logs-actions\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>setLogs([]),children:\"Clear Logs\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"logs-container\",children:logs.length===0?/*#__PURE__*/_jsx(\"p\",{className:\"no-logs\",children:\"No logs available. Start sync to see output.\"}):logs.map((log,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"log-entry\",children:log},index))})]})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "App", "config", "setConfig", "syncStatus", "setSyncStatus", "isRunning", "logs", "setLogs", "activeTab", "setActiveTab", "loading", "setLoading", "loadData", "configData", "statusData", "Promise", "all", "window", "electronAPI", "getConfig", "getSyncStatus", "error", "console", "unsubscribeStatus", "onSyncStatusChanged", "event", "data", "unsubscribeOutput", "onPythonOutput", "timestamp", "Date", "toLocaleTimeString", "logEntry", "concat", "type", "toUpperCase", "prev", "slice", "handleStartSync", "result", "startSync", "success", "alert", "handleStopSync", "stopSync", "handleSaveConfig", "saveConfig", "updateConfig", "path", "value", "keys", "split", "newConfig", "_objectSpread", "current", "i", "length", "className", "children", "pid", "onClick", "checked", "sender", "enabled", "onChange", "e", "target", "ntfy_topic_url", "placeholder", "poll_interval_seconds", "parseFloat", "min", "step", "receiver", "ntfy_server", "ntfy_topic", "logging", "level", "macos", "image_support", "disabled", "map", "log", "index"], "sources": ["/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\n\n// Type definitions\ninterface Config {\n  sender: {\n    enabled: boolean;\n    ntfy_topic_url: string;\n    poll_interval_seconds: number;\n    request_timeout_seconds: number;\n    filename_prefix: string;\n  };\n  receiver: {\n    enabled: boolean;\n    ntfy_server: string;\n    ntfy_topic: string;\n    reconnect_delay_seconds: number;\n    request_timeout_seconds: number;\n  };\n  logging: {\n    level: string;\n  };\n  macos: {\n    image_support: boolean;\n  };\n}\n\ninterface SyncStatus {\n  isRunning: boolean;\n  pid?: number;\n}\n\ninterface PythonOutput {\n  type: 'stdout' | 'stderr';\n  data: string;\n}\n\n// Declare global electronAPI\ndeclare global {\n  interface Window {\n    electronAPI: {\n      startSync: (config?: Config) => Promise<{ success: boolean; error?: string }>;\n      stopSync: () => Promise<{ success: boolean; error?: string }>;\n      getSyncStatus: () => Promise<SyncStatus>;\n      getConfig: () => Promise<Config>;\n      saveConfig: (config: Config) => Promise<{ success: boolean }>;\n      onSyncStatusChanged: (callback: (event: any, data: SyncStatus) => void) => () => void;\n      onPythonOutput: (callback: (event: any, data: PythonOutput) => void) => () => void;\n    };\n  }\n}\n\nfunction App() {\n  const [config, setConfig] = useState<Config | null>(null);\n  const [syncStatus, setSyncStatus] = useState<SyncStatus>({ isRunning: false });\n  const [logs, setLogs] = useState<string[]>([]);\n  const [activeTab, setActiveTab] = useState<'config' | 'status' | 'logs'>('config');\n  const [loading, setLoading] = useState(false);\n\n  // Load initial data\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        const [configData, statusData] = await Promise.all([\n          window.electronAPI.getConfig(),\n          window.electronAPI.getSyncStatus()\n        ]);\n        setConfig(configData);\n        setSyncStatus(statusData);\n      } catch (error) {\n        console.error('Failed to load initial data:', error);\n      }\n    };\n\n    loadData();\n\n    // Set up event listeners\n    const unsubscribeStatus = window.electronAPI.onSyncStatusChanged((event, data) => {\n      setSyncStatus(data);\n    });\n\n    const unsubscribeOutput = window.electronAPI.onPythonOutput((event, data) => {\n      const timestamp = new Date().toLocaleTimeString();\n      const logEntry = `[${timestamp}] ${data.type.toUpperCase()}: ${data.data}`;\n      setLogs(prev => [...prev.slice(-99), logEntry]); // Keep last 100 logs\n    });\n\n    return () => {\n      unsubscribeStatus();\n      unsubscribeOutput();\n    };\n  }, []);\n\n  const handleStartSync = async () => {\n    if (!config) return;\n\n    setLoading(true);\n    try {\n      const result = await window.electronAPI.startSync(config);\n      if (!result.success) {\n        alert(`Failed to start sync: ${result.error}`);\n      }\n    } catch (error) {\n      alert(`Error starting sync: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStopSync = async () => {\n    setLoading(true);\n    try {\n      const result = await window.electronAPI.stopSync();\n      if (!result.success) {\n        alert(`Failed to stop sync: ${result.error}`);\n      }\n    } catch (error) {\n      alert(`Error stopping sync: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveConfig = async () => {\n    if (!config) return;\n\n    setLoading(true);\n    try {\n      const result = await window.electronAPI.saveConfig(config);\n      if (result.success) {\n        alert('Configuration saved successfully!');\n      } else {\n        alert('Failed to save configuration');\n      }\n    } catch (error) {\n      alert(`Error saving configuration: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateConfig = (path: string, value: any) => {\n    if (!config) return;\n\n    const keys = path.split('.');\n    const newConfig = { ...config };\n    let current: any = newConfig;\n\n    for (let i = 0; i < keys.length - 1; i++) {\n      current = current[keys[i]];\n    }\n    current[keys[keys.length - 1]] = value;\n\n    setConfig(newConfig);\n  };\n\n  if (!config) {\n    return (\n      <div className=\"app-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading configuration...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <h1>Clipboard Sync</h1>\n        <div className=\"status-indicator\">\n          <div className={`status-dot ${syncStatus.isRunning ? 'running' : 'stopped'}`}></div>\n          <span>{syncStatus.isRunning ? 'Running' : 'Stopped'}</span>\n          {syncStatus.pid && <span className=\"pid\">PID: {syncStatus.pid}</span>}\n        </div>\n      </header>\n\n      <nav className=\"app-nav\">\n        <button\n          className={activeTab === 'config' ? 'active' : ''}\n          onClick={() => setActiveTab('config')}\n        >\n          Configuration\n        </button>\n        <button\n          className={activeTab === 'status' ? 'active' : ''}\n          onClick={() => setActiveTab('status')}\n        >\n          Status\n        </button>\n        <button\n          className={activeTab === 'logs' ? 'active' : ''}\n          onClick={() => setActiveTab('logs')}\n        >\n          Logs\n        </button>\n      </nav>\n\n      <main className=\"app-main\">\n        {activeTab === 'config' && (\n          <div className=\"config-panel\">\n            <h2>Configuration</h2>\n\n            <div className=\"config-section\">\n              <h3>Sender Settings</h3>\n              <label>\n                <input\n                  type=\"checkbox\"\n                  checked={config.sender.enabled}\n                  onChange={(e) => updateConfig('sender.enabled', e.target.checked)}\n                />\n                Enable Sender\n              </label>\n\n              <label>\n                Ntfy Topic URL:\n                <input\n                  type=\"text\"\n                  value={config.sender.ntfy_topic_url}\n                  onChange={(e) => updateConfig('sender.ntfy_topic_url', e.target.value)}\n                  placeholder=\"https://ntfy.sh/your_topic_here\"\n                />\n              </label>\n\n              <label>\n                Poll Interval (seconds):\n                <input\n                  type=\"number\"\n                  value={config.sender.poll_interval_seconds}\n                  onChange={(e) => updateConfig('sender.poll_interval_seconds', parseFloat(e.target.value))}\n                  min=\"0.1\"\n                  step=\"0.1\"\n                />\n              </label>\n            </div>\n\n            <div className=\"config-section\">\n              <h3>Receiver Settings</h3>\n              <label>\n                <input\n                  type=\"checkbox\"\n                  checked={config.receiver.enabled}\n                  onChange={(e) => updateConfig('receiver.enabled', e.target.checked)}\n                />\n                Enable Receiver\n              </label>\n\n              <label>\n                Ntfy Server:\n                <input\n                  type=\"text\"\n                  value={config.receiver.ntfy_server}\n                  onChange={(e) => updateConfig('receiver.ntfy_server', e.target.value)}\n                  placeholder=\"ntfy.sh\"\n                />\n              </label>\n\n              <label>\n                Ntfy Topic:\n                <input\n                  type=\"text\"\n                  value={config.receiver.ntfy_topic}\n                  onChange={(e) => updateConfig('receiver.ntfy_topic', e.target.value)}\n                  placeholder=\"your_receive_topic_here\"\n                />\n              </label>\n            </div>\n\n            <div className=\"config-section\">\n              <h3>Advanced Settings</h3>\n              <label>\n                Log Level:\n                <select\n                  value={config.logging.level}\n                  onChange={(e) => updateConfig('logging.level', e.target.value)}\n                >\n                  <option value=\"DEBUG\">Debug</option>\n                  <option value=\"INFO\">Info</option>\n                  <option value=\"WARNING\">Warning</option>\n                  <option value=\"ERROR\">Error</option>\n                </select>\n              </label>\n\n              <label>\n                <input\n                  type=\"checkbox\"\n                  checked={config.macos.image_support}\n                  onChange={(e) => updateConfig('macos.image_support', e.target.checked)}\n                />\n                Enable macOS Image Support\n              </label>\n            </div>\n\n            <div className=\"config-actions\">\n              <button onClick={handleSaveConfig} disabled={loading}>\n                {loading ? 'Saving...' : 'Save Configuration'}\n              </button>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'status' && (\n          <div className=\"status-panel\">\n            <h2>Sync Status</h2>\n\n            <div className=\"status-info\">\n              <div className=\"status-item\">\n                <strong>Status:</strong>\n                <span className={syncStatus.isRunning ? 'status-running' : 'status-stopped'}>\n                  {syncStatus.isRunning ? 'Running' : 'Stopped'}\n                </span>\n              </div>\n\n              {syncStatus.pid && (\n                <div className=\"status-item\">\n                  <strong>Process ID:</strong> {syncStatus.pid}\n                </div>\n              )}\n\n              <div className=\"status-item\">\n                <strong>Sender:</strong> {config.sender.enabled ? 'Enabled' : 'Disabled'}\n              </div>\n\n              <div className=\"status-item\">\n                <strong>Receiver:</strong> {config.receiver.enabled ? 'Enabled' : 'Disabled'}\n              </div>\n            </div>\n\n            <div className=\"control-buttons\">\n              <button\n                onClick={handleStartSync}\n                disabled={loading || syncStatus.isRunning}\n                className=\"start-button\"\n              >\n                {loading ? 'Starting...' : 'Start Sync'}\n              </button>\n\n              <button\n                onClick={handleStopSync}\n                disabled={loading || !syncStatus.isRunning}\n                className=\"stop-button\"\n              >\n                {loading ? 'Stopping...' : 'Stop Sync'}\n              </button>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'logs' && (\n          <div className=\"logs-panel\">\n            <h2>Logs</h2>\n\n            <div className=\"logs-actions\">\n              <button onClick={() => setLogs([])}>Clear Logs</button>\n            </div>\n\n            <div className=\"logs-container\">\n              {logs.length === 0 ? (\n                <p className=\"no-logs\">No logs available. Start sync to see output.</p>\n              ) : (\n                logs.map((log, index) => (\n                  <div key={index} className=\"log-entry\">\n                    {log}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "2JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,WAAW,CAElB;AAkCA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAeA,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGR,QAAQ,CAAgB,IAAI,CAAC,CACzD,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGV,QAAQ,CAAa,CAAEW,SAAS,CAAE,KAAM,CAAC,CAAC,CAC9E,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAW,EAAE,CAAC,CAC9C,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAA+B,QAAQ,CAAC,CAClF,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAE7C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACF,KAAM,CAACC,UAAU,CAAEC,UAAU,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACjDC,MAAM,CAACC,WAAW,CAACC,SAAS,CAAC,CAAC,CAC9BF,MAAM,CAACC,WAAW,CAACE,aAAa,CAAC,CAAC,CACnC,CAAC,CACFlB,SAAS,CAACW,UAAU,CAAC,CACrBT,aAAa,CAACU,UAAU,CAAC,CAC3B,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAEDT,QAAQ,CAAC,CAAC,CAEV;AACA,KAAM,CAAAW,iBAAiB,CAAGN,MAAM,CAACC,WAAW,CAACM,mBAAmB,CAAC,CAACC,KAAK,CAAEC,IAAI,GAAK,CAChFtB,aAAa,CAACsB,IAAI,CAAC,CACrB,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAGV,MAAM,CAACC,WAAW,CAACU,cAAc,CAAC,CAACH,KAAK,CAAEC,IAAI,GAAK,CAC3E,KAAM,CAAAG,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CACjD,KAAM,CAAAC,QAAQ,KAAAC,MAAA,CAAOJ,SAAS,OAAAI,MAAA,CAAKP,IAAI,CAACQ,IAAI,CAACC,WAAW,CAAC,CAAC,OAAAF,MAAA,CAAKP,IAAI,CAACA,IAAI,CAAE,CAC1EnB,OAAO,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAEL,QAAQ,CAAC,CAAC,CAAE;AACnD,CAAC,CAAC,CAEF,MAAO,IAAM,CACXT,iBAAiB,CAAC,CAAC,CACnBI,iBAAiB,CAAC,CAAC,CACrB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACrC,MAAM,CAAE,OAEbU,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,KAAM,CAAAtB,MAAM,CAACC,WAAW,CAACsB,SAAS,CAACvC,MAAM,CAAC,CACzD,GAAI,CAACsC,MAAM,CAACE,OAAO,CAAE,CACnBC,KAAK,0BAAAT,MAAA,CAA0BM,MAAM,CAAClB,KAAK,CAAE,CAAC,CAChD,CACF,CAAE,MAAOA,KAAK,CAAE,CACdqB,KAAK,yBAAAT,MAAA,CAAyBZ,KAAK,CAAE,CAAC,CACxC,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjChC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,KAAM,CAAAtB,MAAM,CAACC,WAAW,CAAC0B,QAAQ,CAAC,CAAC,CAClD,GAAI,CAACL,MAAM,CAACE,OAAO,CAAE,CACnBC,KAAK,yBAAAT,MAAA,CAAyBM,MAAM,CAAClB,KAAK,CAAE,CAAC,CAC/C,CACF,CAAE,MAAOA,KAAK,CAAE,CACdqB,KAAK,yBAAAT,MAAA,CAAyBZ,KAAK,CAAE,CAAC,CACxC,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkC,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAC5C,MAAM,CAAE,OAEbU,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,KAAM,CAAAtB,MAAM,CAACC,WAAW,CAAC4B,UAAU,CAAC7C,MAAM,CAAC,CAC1D,GAAIsC,MAAM,CAACE,OAAO,CAAE,CAClBC,KAAK,CAAC,mCAAmC,CAAC,CAC5C,CAAC,IAAM,CACLA,KAAK,CAAC,8BAA8B,CAAC,CACvC,CACF,CAAE,MAAOrB,KAAK,CAAE,CACdqB,KAAK,gCAAAT,MAAA,CAAgCZ,KAAK,CAAE,CAAC,CAC/C,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoC,YAAY,CAAGA,CAACC,IAAY,CAAEC,KAAU,GAAK,CACjD,GAAI,CAAChD,MAAM,CAAE,OAEb,KAAM,CAAAiD,IAAI,CAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAC5B,KAAM,CAAAC,SAAS,CAAAC,aAAA,IAAQpD,MAAM,CAAE,CAC/B,GAAI,CAAAqD,OAAY,CAAGF,SAAS,CAE5B,IAAK,GAAI,CAAAG,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGL,IAAI,CAACM,MAAM,CAAG,CAAC,CAAED,CAAC,EAAE,CAAE,CACxCD,OAAO,CAAGA,OAAO,CAACJ,IAAI,CAACK,CAAC,CAAC,CAAC,CAC5B,CACAD,OAAO,CAACJ,IAAI,CAACA,IAAI,CAACM,MAAM,CAAG,CAAC,CAAC,CAAC,CAAGP,KAAK,CAEtC/C,SAAS,CAACkD,SAAS,CAAC,CACtB,CAAC,CAED,GAAI,CAACnD,MAAM,CAAE,CACX,mBACEF,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7D,IAAA,QAAK4D,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC5D,IAAA,MAAA6D,QAAA,CAAG,0BAAwB,CAAG,CAAC,EAC5B,CAAC,CAEV,CAEA,mBACE3D,KAAA,QAAK0D,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClB3D,KAAA,WAAQ0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC5B7D,IAAA,OAAA6D,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB3D,KAAA,QAAK0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B7D,IAAA,QAAK4D,SAAS,eAAAxB,MAAA,CAAgB9B,UAAU,CAACE,SAAS,CAAG,SAAS,CAAG,SAAS,CAAG,CAAM,CAAC,cACpFR,IAAA,SAAA6D,QAAA,CAAOvD,UAAU,CAACE,SAAS,CAAG,SAAS,CAAG,SAAS,CAAO,CAAC,CAC1DF,UAAU,CAACwD,GAAG,eAAI5D,KAAA,SAAM0D,SAAS,CAAC,KAAK,CAAAC,QAAA,EAAC,OAAK,CAACvD,UAAU,CAACwD,GAAG,EAAO,CAAC,EAClE,CAAC,EACA,CAAC,cAET5D,KAAA,QAAK0D,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB7D,IAAA,WACE4D,SAAS,CAAEjD,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAG,CAClDoD,OAAO,CAAEA,CAAA,GAAMnD,YAAY,CAAC,QAAQ,CAAE,CAAAiD,QAAA,CACvC,eAED,CAAQ,CAAC,cACT7D,IAAA,WACE4D,SAAS,CAAEjD,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAG,CAClDoD,OAAO,CAAEA,CAAA,GAAMnD,YAAY,CAAC,QAAQ,CAAE,CAAAiD,QAAA,CACvC,QAED,CAAQ,CAAC,cACT7D,IAAA,WACE4D,SAAS,CAAEjD,SAAS,GAAK,MAAM,CAAG,QAAQ,CAAG,EAAG,CAChDoD,OAAO,CAAEA,CAAA,GAAMnD,YAAY,CAAC,MAAM,CAAE,CAAAiD,QAAA,CACrC,MAED,CAAQ,CAAC,EACN,CAAC,cAEN3D,KAAA,SAAM0D,SAAS,CAAC,UAAU,CAAAC,QAAA,EACvBlD,SAAS,GAAK,QAAQ,eACrBT,KAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7D,IAAA,OAAA6D,QAAA,CAAI,eAAa,CAAI,CAAC,cAEtB3D,KAAA,QAAK0D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7D,IAAA,OAAA6D,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB3D,KAAA,UAAA2D,QAAA,eACE7D,IAAA,UACEqC,IAAI,CAAC,UAAU,CACf2B,OAAO,CAAE5D,MAAM,CAAC6D,MAAM,CAACC,OAAQ,CAC/BC,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,gBAAgB,CAAEkB,CAAC,CAACC,MAAM,CAACL,OAAO,CAAE,CACnE,CAAC,gBAEJ,EAAO,CAAC,cAER9D,KAAA,UAAA2D,QAAA,EAAO,iBAEL,cAAA7D,IAAA,UACEqC,IAAI,CAAC,MAAM,CACXe,KAAK,CAAEhD,MAAM,CAAC6D,MAAM,CAACK,cAAe,CACpCH,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,uBAAuB,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CACvEmB,WAAW,CAAC,iCAAiC,CAC9C,CAAC,EACG,CAAC,cAERrE,KAAA,UAAA2D,QAAA,EAAO,0BAEL,cAAA7D,IAAA,UACEqC,IAAI,CAAC,QAAQ,CACbe,KAAK,CAAEhD,MAAM,CAAC6D,MAAM,CAACO,qBAAsB,CAC3CL,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,8BAA8B,CAAEuB,UAAU,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,CAAE,CAC1FsB,GAAG,CAAC,KAAK,CACTC,IAAI,CAAC,KAAK,CACX,CAAC,EACG,CAAC,EACL,CAAC,cAENzE,KAAA,QAAK0D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7D,IAAA,OAAA6D,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B3D,KAAA,UAAA2D,QAAA,eACE7D,IAAA,UACEqC,IAAI,CAAC,UAAU,CACf2B,OAAO,CAAE5D,MAAM,CAACwE,QAAQ,CAACV,OAAQ,CACjCC,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,kBAAkB,CAAEkB,CAAC,CAACC,MAAM,CAACL,OAAO,CAAE,CACrE,CAAC,kBAEJ,EAAO,CAAC,cAER9D,KAAA,UAAA2D,QAAA,EAAO,cAEL,cAAA7D,IAAA,UACEqC,IAAI,CAAC,MAAM,CACXe,KAAK,CAAEhD,MAAM,CAACwE,QAAQ,CAACC,WAAY,CACnCV,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,sBAAsB,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CACtEmB,WAAW,CAAC,SAAS,CACtB,CAAC,EACG,CAAC,cAERrE,KAAA,UAAA2D,QAAA,EAAO,aAEL,cAAA7D,IAAA,UACEqC,IAAI,CAAC,MAAM,CACXe,KAAK,CAAEhD,MAAM,CAACwE,QAAQ,CAACE,UAAW,CAClCX,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,qBAAqB,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CACrEmB,WAAW,CAAC,yBAAyB,CACtC,CAAC,EACG,CAAC,EACL,CAAC,cAENrE,KAAA,QAAK0D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7D,IAAA,OAAA6D,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B3D,KAAA,UAAA2D,QAAA,EAAO,YAEL,cAAA3D,KAAA,WACEkD,KAAK,CAAEhD,MAAM,CAAC2E,OAAO,CAACC,KAAM,CAC5Bb,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,eAAe,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CAAAS,QAAA,eAE/D7D,IAAA,WAAQoD,KAAK,CAAC,OAAO,CAAAS,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpC7D,IAAA,WAAQoD,KAAK,CAAC,MAAM,CAAAS,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC7D,IAAA,WAAQoD,KAAK,CAAC,SAAS,CAAAS,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC7D,IAAA,WAAQoD,KAAK,CAAC,OAAO,CAAAS,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACJ,CAAC,cAER3D,KAAA,UAAA2D,QAAA,eACE7D,IAAA,UACEqC,IAAI,CAAC,UAAU,CACf2B,OAAO,CAAE5D,MAAM,CAAC6E,KAAK,CAACC,aAAc,CACpCf,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,qBAAqB,CAAEkB,CAAC,CAACC,MAAM,CAACL,OAAO,CAAE,CACxE,CAAC,6BAEJ,EAAO,CAAC,EACL,CAAC,cAENhE,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B7D,IAAA,WAAQ+D,OAAO,CAAEf,gBAAiB,CAACmC,QAAQ,CAAEtE,OAAQ,CAAAgD,QAAA,CAClDhD,OAAO,CAAG,WAAW,CAAG,oBAAoB,CACvC,CAAC,CACN,CAAC,EACH,CACN,CAEAF,SAAS,GAAK,QAAQ,eACrBT,KAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7D,IAAA,OAAA6D,QAAA,CAAI,aAAW,CAAI,CAAC,cAEpB3D,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3D,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7D,IAAA,WAAA6D,QAAA,CAAQ,SAAO,CAAQ,CAAC,cACxB7D,IAAA,SAAM4D,SAAS,CAAEtD,UAAU,CAACE,SAAS,CAAG,gBAAgB,CAAG,gBAAiB,CAAAqD,QAAA,CACzEvD,UAAU,CAACE,SAAS,CAAG,SAAS,CAAG,SAAS,CACzC,CAAC,EACJ,CAAC,CAELF,UAAU,CAACwD,GAAG,eACb5D,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7D,IAAA,WAAA6D,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAACvD,UAAU,CAACwD,GAAG,EACzC,CACN,cAED5D,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7D,IAAA,WAAA6D,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACzD,MAAM,CAAC6D,MAAM,CAACC,OAAO,CAAG,SAAS,CAAG,UAAU,EACrE,CAAC,cAENhE,KAAA,QAAK0D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7D,IAAA,WAAA6D,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAACzD,MAAM,CAACwE,QAAQ,CAACV,OAAO,CAAG,SAAS,CAAG,UAAU,EACzE,CAAC,EACH,CAAC,cAENhE,KAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B7D,IAAA,WACE+D,OAAO,CAAEtB,eAAgB,CACzB0C,QAAQ,CAAEtE,OAAO,EAAIP,UAAU,CAACE,SAAU,CAC1CoD,SAAS,CAAC,cAAc,CAAAC,QAAA,CAEvBhD,OAAO,CAAG,aAAa,CAAG,YAAY,CACjC,CAAC,cAETb,IAAA,WACE+D,OAAO,CAAEjB,cAAe,CACxBqC,QAAQ,CAAEtE,OAAO,EAAI,CAACP,UAAU,CAACE,SAAU,CAC3CoD,SAAS,CAAC,aAAa,CAAAC,QAAA,CAEtBhD,OAAO,CAAG,aAAa,CAAG,WAAW,CAChC,CAAC,EACN,CAAC,EACH,CACN,CAEAF,SAAS,GAAK,MAAM,eACnBT,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB7D,IAAA,OAAA6D,QAAA,CAAI,MAAI,CAAI,CAAC,cAEb7D,IAAA,QAAK4D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B7D,IAAA,WAAQ+D,OAAO,CAAEA,CAAA,GAAMrD,OAAO,CAAC,EAAE,CAAE,CAAAmD,QAAA,CAAC,YAAU,CAAQ,CAAC,CACpD,CAAC,cAEN7D,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BpD,IAAI,CAACkD,MAAM,GAAK,CAAC,cAChB3D,IAAA,MAAG4D,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,8CAA4C,CAAG,CAAC,CAEvEpD,IAAI,CAAC2E,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,gBAClBtF,IAAA,QAAiB4D,SAAS,CAAC,WAAW,CAAAC,QAAA,CACnCwB,GAAG,EADIC,KAEL,CACN,CACF,CACE,CAAC,EACH,CACN,EACG,CAAC,EACJ,CAAC,CAEV,CAEA,cAAe,CAAAnF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}