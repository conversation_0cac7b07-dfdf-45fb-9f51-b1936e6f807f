[{"/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/index.tsx": "1", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/App.tsx": "3"}, {"size": 554, "mtime": 1751524366691, "results": "4", "hashOfConfig": "5"}, {"size": 425, "mtime": 1751524366691, "results": "6", "hashOfConfig": "5"}, {"size": 11080, "mtime": 1751524782497, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8qg714", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/index.tsx", [], [], "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/gui/src/renderer/src/App.tsx", [], []]