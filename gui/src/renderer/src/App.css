/* App Layout */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
}

.app-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ccc;
}

.status-dot.running {
  background-color: #34c759;
  box-shadow: 0 0 8px rgba(52, 199, 89, 0.4);
}

.status-dot.stopped {
  background-color: #ff3b30;
}

.pid {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 12px;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Navigation */
.app-nav {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.app-nav button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background-color: transparent;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.app-nav button:hover {
  background-color: #f8f8f8;
  color: #333;
}

.app-nav button.active {
  background-color: #007aff;
  color: white;
}

/* Main Content */
.app-main {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Configuration Panel */
.config-panel {
  max-width: 600px;
  margin: 0 auto;
}

.config-panel h2 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.config-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.config-section label {
  display: block;
  margin-bottom: 12px;
  font-size: 14px;
  color: #333;
}

.config-section input[type="text"],
.config-section input[type="number"],
.config-section select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  font-size: 14px;
  margin-top: 4px;
  transition: border-color 0.2s ease;
}

.config-section input[type="text"]:focus,
.config-section input[type="number"]:focus,
.config-section select:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.config-section input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

.config-actions {
  text-align: center;
  margin-top: 24px;
}

.config-actions button {
  background-color: #007aff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.config-actions button:hover:not(:disabled) {
  background-color: #0056cc;
}

.config-actions button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Status Panel */
.status-panel {
  max-width: 500px;
  margin: 0 auto;
}

.status-panel h2 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.status-info {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-running {
  color: #34c759;
  font-weight: 500;
}

.status-stopped {
  color: #ff3b30;
  font-weight: 500;
}

.control-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.control-buttons button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.start-button {
  background-color: #34c759;
  color: white;
}

.start-button:hover:not(:disabled) {
  background-color: #28a745;
}

.stop-button {
  background-color: #ff3b30;
  color: white;
}

.stop-button:hover:not(:disabled) {
  background-color: #dc3545;
}

.control-buttons button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Logs Panel */
.logs-panel {
  max-width: 800px;
  margin: 0 auto;
}

.logs-panel h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.logs-actions {
  margin-bottom: 16px;
}

.logs-actions button {
  background-color: #666;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logs-actions button:hover {
  background-color: #555;
}

.logs-container {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  height: 400px;
  overflow-y: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  color: #d4d4d4;
  margin-bottom: 2px;
  word-wrap: break-word;
}

.no-logs {
  color: #888;
  text-align: center;
  font-style: italic;
  margin: 40px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }

  .config-section,
  .status-info {
    padding: 16px;
  }

  .control-buttons {
    flex-direction: column;
  }

  .control-buttons button {
    width: 100%;
  }
}
