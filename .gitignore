# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Environments
.env
.venv
venv/
env/
ENV/
pip-freeze.txt
pipenv-setup.lock

# Distribution / packaging
.eggs/
*.egg-info/
dist/
build/
*.egg
*.whl

# Config files (sensitive data might be stored here)
config/config.yaml

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs
.idea/
.vscode/
*.swp
*.swo

.aider*

# Node.js / Electron / React specific
# Dependencies
/gui/node_modules
/gui/src/renderer/node_modules

# Build outputs
/gui/src/renderer/build

# Lockfiles
# It is recommended to commit lockfiles (e.g., package-lock.json) for applications
# to ensure reproducible builds. The rules ignoring them have been removed.

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Swift / Xcode
clipboard-sync-swift/DerivedData/
*.xcodeproj/project.xcworkspace
*.xcodeproj/xcuserdata/

# GUI build output
/gui/dist/
